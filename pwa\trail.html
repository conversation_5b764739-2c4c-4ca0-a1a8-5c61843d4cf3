<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <title></title>
    <link href="css/bootstrap.min.css" rel="stylesheet">
    <link href="css/font-awesome.min.css" rel="stylesheet">
    <link href="css/bootstrap-slider.min.css" rel="stylesheet">
    <script type="text/javascript">
        document.write('<link rel="stylesheet" href="css/style.css?time=' + new Date().getTime() + '">');
    </script>
    <script src="js/jquery-3.7.1.min.js"></script>
    <script src="js/jquery-lang.js" charset="utf-8" type="text/javascript"></script>
    <script src="js/sweetalert2.all.min.js"></script>
    <script src="js/common/common.js"></script>
</head>

<body>
    <!-- 顶部栏 -->
    <header class="header">
        <div class="header-title"><i class="fa fa-bolt" aria-hidden="true"></i> </div>
    </header>


    <div class="content trail_content">
        <div class="status text-center">
            <img src="img/green-ball.gif" class="img-responsive center-block">
        </div>

    </div>

    <div class="container text-center">
        <!-- 文字动画容器 -->
        <div class="animate-text mb-8 text-center" lang="en_US">trailStartCharingHtml</div>

        <p class="subtext" lang="en_US">verifyingCharger</p>
    </div>

    <div class="bottom_text text-center" lang="en_US">trailBottomText</div>

    <!-- 页面加载动画 -->
    <div id="pageLoadingOverlay">
        <div class="charging-container">
            <i class="fa fa-car car-icon"></i>
            <div class="loading-text"></div>
        </div>
    </div>

    <!-- 加载动画 -->
    <div id="loadingOverlay">
        <div class="loading-spinner"></div>
        <div class="loading-text"></div>
    </div>
</body>
<script>
    var currentSiteNumber = getLocalStorage('site'); // 场地编号
    var currentConnectorNumber = getLocalStorage('connector'); // 充电枪编号
    var settingToken = getLocalStorage('settingToken'); // 充电枪setting_token
    var prePaidSelectedChargeValue = getLocalStorage('prePaidSelectedChargeValue'); // 预付已选充电量
    var connectorInfo = null; // 充电枪信息
    var lastUrl = 'choose_plug.html';
    var nextUrl = 'charging.html';
    const timeout = 1 * 60 * 1000; // 1分钟 轮询的超时时间
    const interval = 2 * 1000; // 2秒 轮询的轮询间隔

    $(document).ready(function () {
        // 初始化语言包，默认会回调pageReady函数(如存在)
        initLang();
    });

    // 页面初始化
    function pageReady() {
        // 如果为空，则跳转至choose_plug.html
        if (isEmptyString(currentSiteNumber) || isEmptyString(currentConnectorNumber) || isEmptyString(prePaidSelectedChargeValue)) {
            window.location.href = `choose_plug.html?site=${currentSiteNumber || getQueryVariable('site', '')}`;
            return;
        }
        if (!isLocalStorageAvailable()) {
            lastUrl += `?site=${currentSiteNumber}&connector=${currentConnectorNumber}`;
            nextUrl += `?site=${currentSiteNumber}&connector=${currentConnectorNumber}`;
        }
        // 获取充电枪信息，验证setting_token，并远程开始充电
        getConnector(currentConnectorNumber).then(() => {
            setTimeout(() => { // 延迟执行，防止在页面loading动画消失前就跳走
                verifyConnectorStartCharge(currentConnectorNumber, settingToken).then(() => {
                    const uniqueId = generateUniqueId();
                    remoteStart(uniqueId, currentConnectorNumber);
                });
            }, 500);
        });
    }

    // 获取充电枪信息
    function getConnector(connectorNumber) {
        return new Promise((resolve, reject) => {
            ajaxPostRequestJsonAndCallback({
                url: "api/getConnector.php",
                params: {
                    language_code: currentLanguageCode,
                    connector_number: connectorNumber,
                },
                successCallback: function (data) {
                    connectorInfo = data.data;
                    $("title").text(connectorInfo.name);
                    $(".header > .header-title").append(connectorInfo.name);
                    if (connectorInfo?.connector_setting?.charge_tariff_scheme !== 'PRE_PAID') {
                        Swal.fire({
                            title: window.lang.translate('warning'),
                            text: window.lang.translate('notSupported'),
                            icon: 'warning',
                            showConfirmButton: false,
                            timer: 3000,
                            timerProgressBar: true,
                            willClose: function () {
                                // 跳回充电枪列表页
                                window.location.href = lastUrl;
                            }
                        });
                        reject(connectorInfo);
                    }
                    resolve(connectorInfo);
                },
                errorCallback: function (message) {
                    Swal.fire({
                        title: window.lang.translate('warning'),
                        confirmButtonText: window.lang.translate('btnConfirm'),
                        confirmButtonColor: '#54C49C',
                        text: message,
                        icon: 'error',
                        allowOutsideClick: false,
                        showCancelButton: true,
                        cancelButtonText: window.lang.translate('btnGoBack') // 自定义返回按钮文本
                    }).then(function (result) {
                        if (result.isConfirmed) {
                            // 点击确认按钮，刷新当前页面
                            window.location.reload();
                        } else if (result.dismiss === Swal.DismissReason.cancel) {
                            // 点击返回按钮，跳转到上一页
                            window.location.href = lastUrl;
                        }
                    });
                    reject(message);
                }
            });
        })
    }

    // 验证充电枪setting_token
    function verifyConnectorStartCharge(connectorNumber, settingToken) {
        return new Promise((resolve, reject) => {
            ajaxPostRequestJsonAndCallback({
                url: "api/verifyConnectorStartCharge.php",
                params: {
                    language_code: currentLanguageCode,
                    connector_number: connectorNumber,
                    setting_token: settingToken,
                },
                successCallback: function (data) {
                    if (data.data === true) {
                        resolve(data);
                    } else {
                        Swal.fire({
                            title: data?.message || window.lang.translate('warning'),
                            text: window.lang.translate('tryAgain'),
                            icon: 'warning',
                            showConfirmButton: false,
                            timer: 3000,
                            timerProgressBar: true,
                            willClose: function () {
                                // 跳回充电枪列表页
                                window.location.href = lastUrl;
                            }
                        });
                        reject(data);
                    }
                },
                errorCallback: function (message) {
                    Swal.fire({
                        title: message || window.lang.translate('warning'),
                        text: window.lang.translate('tryAgain'),
                        icon: 'warning',
                        showConfirmButton: false,
                        timer: 3000,
                        timerProgressBar: true,
                        willClose: function () {
                            // 跳回充电枪列表页
                            window.location.href = lastUrl;
                        }
                    });
                    reject(message);
                }
            });
        })
    }

    // 远程开始充电并轮询结果
    function remoteStart(uniqueId, currentConnectorNumber) {
        let timeoutId;
        let expired = false;

        // 超时逻辑
        timeoutId = setTimeout(() => {
            expired = true;
            Swal.fire({
                title: window.lang.translate('warning'),
                text: window.lang.translate('timeout'),
                icon: 'warning',
                showConfirmButton: false,
                timer: 2000,
                timerProgressBar: true,
                willClose: function () {
                    // 跳回充电枪列表页
                    window.location.href = lastUrl;
                }
            });

        }, timeout);

        // 开始轮询
        function pollStart() {
            if (expired) return; // 超时了就别继续了

            getCallChargePointRecordStart(uniqueId).then(() => {
                if (expired) return;
                clearTimeout(timeoutId);
                getConnectorCurrentChargeRecordNumber(currentConnectorNumber).then((currentChargeRecordNumber) => {
                    updateChargeRecordPrePaidSelectedChargeValue(currentChargeRecordNumber, prePaidSelectedChargeValue).then(() => {
                        removeLocalStorage('settingToken');
                        window.location.href = nextUrl;
                    });
                }).catch(() => {
                    Swal.fire({
                        title: window.lang.translate('warning'),
                        text: window.lang.translate('timeout'),
                        icon: 'warning',
                        showConfirmButton: false,
                        timer: 2000,
                        timerProgressBar: true,
                        willClose: function () {
                            // 跳回充电枪列表页
                            window.location.href = lastUrl;
                        }
                    });
                });
            }).catch(() => {
                setTimeout(() => {
                    pollStart();
                }, interval);
            });
        }

        remoteStartCharge(uniqueId, currentConnectorNumber).then(() => {
            pollStart();
        });
    }

    // 远程开始充电
    function remoteStartCharge(uniqueId, connectorNumber) {
        return new Promise((resolve, reject) => {
            ajaxPostRequestJsonAndCallback({
                url: "api/remoteStartCharge.php",
                params: {
                    language_code: currentLanguageCode,
                    unique_id: uniqueId,
                    connector_number: connectorNumber,
                },
                successCallback: function (data) {
                    if (data.data === true) {
                        resolve(data);
                    } else {
                        Swal.fire({
                            title: data?.message || window.lang.translate('error'),
                            text: window.lang.translate('startChargeFailed'),
                            icon: 'error',
                            showConfirmButton: false,
                            timer: 3000,
                            timerProgressBar: true,
                            willClose: function () {
                                // 跳回充电枪列表页
                                window.location.href = lastUrl;
                            }
                        });
                        reject(data);
                    }
                },
                errorCallback: function (message) {
                    Swal.fire({
                        title: message || window.lang.translate('error'),
                        text: window.lang.translate('startChargeFailed'),
                        icon: 'error',
                        showConfirmButton: false,
                        timer: 3000,
                        timerProgressBar: true,
                        willClose: function () {
                            // 跳回充电枪列表页
                            window.location.href = lastUrl;
                        }
                    });
                    reject(message);
                }
            });
        })
    }

    // 确认开始充电命令是否发送成功
    function getCallChargePointRecordStart(uniqueId) {
        return new Promise((resolve, reject) => {
            ajaxPostRequestJsonAndCallback({
                url: "api/getCallChargePointRecordStart.php",
                params: {
                    language_code: currentLanguageCode,
                    unique_id: uniqueId,
                },
                successCallback: function (data) {
                    if (data.data?.call_verify_result === true) {
                        resolve(data);
                    } else {
                        reject(data);
                    }
                },
                errorCallback: function (message) {
                    reject(message);
                }
            });
        })
    }

    // 轮询获取充电枪当前充电记录编号
    function getConnectorCurrentChargeRecordNumber(connectorNumber) {
        return new Promise((resolve, reject) => {
            const getConnectorCurrentChargeRecordNumberTimer = setInterval(() => {
                ajaxPostRequestJsonAndCallback({
                    url: "api/getConnectorCurrentChargeRecordNumber.php",
                    params: {
                        language_code: currentLanguageCode,
                        connector_number: connectorNumber,
                    },
                    successCallback: function (data) {
                        if (!isEmptyString(data.data)) {
                            const currentChargeRecordNumber = data.data;
                            clearInterval(getConnectorCurrentChargeRecordNumberTimer);
                            clearTimeout(getConnectorCurrentChargeRecordNumberTimeout);
                            resolve(currentChargeRecordNumber);
                        }
                    }
                });
            }, interval);

            const getConnectorCurrentChargeRecordNumberTimeout = setTimeout(() => {
                clearInterval(getConnectorCurrentChargeRecordNumberTimer);
                reject();
            }, timeout);
        });
    }

    // 更新充电记录预付已选充电量
    function updateChargeRecordPrePaidSelectedChargeValue(chargeRecordNumber, prePaidSelectedChargeValue) {
        return new Promise((resolve, reject) => {
            ajaxPostRequestJsonAndCallback({
                url: "api/updateChargeRecordPrePaidSelectedChargeValue.php",
                params: {
                    language_code: currentLanguageCode,
                    charge_record_number: chargeRecordNumber,
                    pre_paid_selected_charge_value: prePaidSelectedChargeValue,
                },
                successCallback: function (data) {
                    if (data.data === true) {
                        removeLocalStorage('prePaidSelectedChargeValue');
                        resolve(data);
                    } else {
                        Swal.fire({
                            title: window.lang.translate('warning'),
                            text: window.lang.translate('requestFailed'),
                            icon: 'warning',
                            allowOutsideClick: false,
                            showCancelButton: true,
                            confirmButtonText: window.lang.translate('btnRetry'),
                            confirmButtonColor: '#54C49C',
                            cancelButtonText: window.lang.translate('cancel'),
                        }).then(function (result) {
                            if (result.isConfirmed) {
                                updateChargeRecordPrePaidSelectedChargeValue(chargeRecordNumber, prePaidSelectedChargeValue);
                            } else {
                                // 跳回充电枪列表页
                                window.location.href = lastUrl;
                            }
                        });
                        reject(data);
                    }
                },
                errorCallback: function (message) {
                    Swal.fire({
                        title: window.lang.translate('warning'),
                        text: window.lang.translate('requestFailed'),
                        icon: 'warning',
                        allowOutsideClick: false,
                        showCancelButton: true,
                        confirmButtonText: window.lang.translate('btnRetry'),
                        confirmButtonColor: '#54C49C',
                        cancelButtonText: window.lang.translate('cancel'),
                    }).then(function (result) {
                        if (result.isConfirmed) {
                            updateChargeRecordPrePaidSelectedChargeValue(chargeRecordNumber, prePaidSelectedChargeValue);
                        } else {
                            // 跳回充电枪列表页
                            window.location.href = lastUrl;
                        }
                    });
                    reject(message);
                }
            });
        })
    }

</script>

</html>
