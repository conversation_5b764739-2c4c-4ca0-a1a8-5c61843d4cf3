var currentLanguageCode = getLocalStorage('currentLanguageCode', 'en_US'); // 当前语言
const defaultLanguageCode = 'en_US'; // 默认语言
var lang = null; // 语言包

// 初始化加载动画
window.addEventListener('DOMContentLoaded', function () {
    showPageLoading();
});

$(document).ready(function () {
    // 如果当前语言为空，则设置为默认语言
    if (!getLocalStorage('currentLanguageCode')) {
        setLocalStorage('currentLanguageCode', defaultLanguageCode);
    }
    // 如果页面包含语言插件
    if ($("#langDropdown").length > 0) {
        const langDropdown = $('#langDropdown');
        // 添加打开事件
        langDropdown.on('show.bs.dropdown', function () {
            // 移除所有语言的active，给当前语言加上active
            langDropdown.parent().find('.dropdown-item').parent().removeClass('active');
            langDropdown.parent().find(`[data-lang="${currentLanguageCode}"]`).parent().addClass('active');
        });
    }
});

// 判断浏览器是否支持localStorage
function isLocalStorageAvailable() {
    try {
        const testKey = '__test__';
        localStorage.setItem(testKey, '1');
        localStorage.removeItem(testKey);
        return true;
    } catch (e) {
        return false;
    }
}

// 获取本地存储
function getLocalStorage(key, defaultValue = null) {
    try {
        const urlVal = getQueryVariable(key, null);   // 先看 URL
        if (urlVal !== null) return urlVal;     // URL 有值直接用

        const localVal = localStorage.getItem(key);
        if (localVal !== null) return localVal; // 本地有值再用
    } catch (err) {
        console.error('Error occurred while obtaining localStorage:', err);
    }

    return defaultValue;
}

// 设置本地存储
function setLocalStorage(key, value) {
    return new Promise((resolve, reject) => {
        try {
            localStorage.setItem(key, value);
            resolve();
        } catch (err) {
            try {
                changeURLStatic(key, value);
                resolve();
            } catch (urlErr) {
                reject(new Error(
                    `Failed to set localStorage: ${storageErr.message}; ` +
                    `and failed to fallback to URL: ${urlErr.message}`
                ));
            }
        }
    });
}

// 清除本地存储
function removeLocalStorage(key) {
    try {
        localStorage.removeItem(key);
    } catch (err) {
        console.error('Error occurred while removing localStorage:', err);
    }
}

//获取url参数
function getQueryVariable(variable, defaultValue = false) {
    var params = new URLSearchParams(window.location.search);
    return params.get(variable) || defaultValue;
}

/**
 * changeURLStatic 修改地址栏 URL参数 不跳转
 * @param name
 * @param value
 */
function changeURLStatic(name, value) {
    let url = changeURLParam(location.href, name, value); // 修改 URL 参数
    history.replaceState(null, null, url);  // 替换地址栏
}

// 判断字符是否为空
function isEmptyString(str) {
    return '' === str || null === str || 'null' === str || undefined === str || 'undefined' === str;
}

// 判断是否为数组且是否为空
function isArrayAndNotEmpty(array) {
    return array && Array.isArray(array) && array.length > 0;
}

// 判断函数是否被定义
function isFunctionDefined(func) {
    return func !== null && func !== undefined && func !== 'undefined';
}

// 判断变量是否被定义
function isVariableDefined(variable) {
    return variable !== null && variable !== undefined && variable !== 'undefined';
}

// 判断对象指定属性是否被定义
function isObjectPropertyDefined(object, property) {
    return object !== null && object !== undefined && object !== 'undefined' && object[property] !== null && object[property] !== undefined && object[property] !== 'undefined';
}

function ajaxPostRequestJsonAndCallback(param) {
    return $.ajax({
        url: isVariableDefined(param.url) ? param.url : '',
        type: "post",
        async: isVariableDefined(param.async) ? param.async : false,
        cache: false,
        contentType: "application/json;charset=utf-8",
        processData: false,
        dataType: "json",
        data: isVariableDefined(param.params) ? JSON.stringify(param.params) : '',
        beforeSend: function () {
            if (isFunctionDefined(param.beforeSendCallback)) {
                param.beforeSendCallback();
            }
        },
        success: function (data) {
            if (data.code === 200) {
                // 如果有成功回调显示成功回调，否则显示弹窗提示操作成功
                if (isFunctionDefined(param.successCallback)) {
                    param.successCallback(data);
                } else {
                    Swal.fire({
                        confirmButtonText: window.lang.translate('btnConfirm'),
                        confirmButtonColor: '#54C49C',
                        text: window.lang.translate('success'),
                        icon: 'success',
                        showCancelButton: false,
                    }).then(function (result) {
                    });
                }
            } else {
                if (isFunctionDefined(param.beforeErrorCallback)) {
                    param.beforeErrorCallback();
                }
                // 如果有错误回调显示错误回调，否则显示弹窗提示错误信息
                if (isFunctionDefined(param.errorCallback)) {
                    param.errorCallback(data.message);
                } else {
                    Swal.fire({
                        title: window.lang.translate('warning'),
                        confirmButtonText: window.lang.translate('btnConfirm'),
                        confirmButtonColor: '#54C49C',
                        text: data.message,
                        icon: 'error',
                        showCancelButton: false,
                    }).then(function (result) {
                    });
                }
            }
        },
        error: function (data) {
            if (isFunctionDefined(param.beforeErrorCallback)) {
                param.beforeErrorCallback();
            }
            var message = data?.responseJSON?.message ?? data?.statusText;
            switch (data.status) {
                case 422:
                    if (isFunctionDefined(param.statusCodeCallback)) {
                        param.statusCodeCallback(data);
                    }
                    return;
                case 401:
                    // 如果接口请求返回 401，刷新页面
                    window.location.reload();
                    return;
                default:
                    break;
            }
            if (isFunctionDefined(param.errorCallback)) {
                param.errorCallback(message);
            } else {
                Swal.fire({
                    title: window.lang.translate('warning'),
                    confirmButtonText: window.lang.translate('btnConfirm'),
                    confirmButtonColor: '#54C49C',
                    text: message,
                    icon: 'error',
                    showCancelButton: false,
                }).then(function (result) {
                });
            }
        },
        complete: function () {
            if (isFunctionDefined(param.completeCallback)) {
                param.completeCallback();
            }
        }
    });
}

// 显示页面加载动画
function showPageLoading() {
    const pageLoadingOverlay = document.getElementById('pageLoadingOverlay');
    pageLoadingOverlay.style.opacity = '1';
    pageLoadingOverlay.style.pointerEvents = 'auto'; // 允许用户与加载动画交互
}

// 隐藏页面加载动画
function hidePageLoading() {
    const pageLoadingOverlay = document.getElementById('pageLoadingOverlay');
    pageLoadingOverlay.style.opacity = '0';
    setTimeout(() => {
        pageLoadingOverlay.style.pointerEvents = 'none'; // 禁止用户与加载动画交互
    }, 500);
}

// 显示加载动画
function showLoading() {
    $('#loadingOverlay').css('display', 'flex');
    $('body').css('overflow', 'hidden');
}

// 隐藏加载动画
function hideLoading() {
    $('#loadingOverlay').css('display', 'none');
    $('body').css('overflow', '');
}

// 初始化多语言
function initLang() {
    // 载入多语言插件
    lang = new Lang();
    // 载入语言包
    lang.dynamic(defaultLanguageCode, `lang/${defaultLanguageCode}.json`);
    // 如果当前语言不为默认语言，则载入当前语言
    if (currentLanguageCode !== defaultLanguageCode) {
        lang.dynamic(currentLanguageCode, `lang/${currentLanguageCode}.json`);
    }
    // 多语言初始化
    lang.init({
        defaultLang: defaultLanguageCode,
        currentLang: currentLanguageCode,
        callbackInit: function () {
            // 页面初始化
            if (isFunctionDefined(pageReady)) {
                pageReady();
            }
            // 隐藏页面加载动画
            setTimeout(() => {
                hidePageLoading();
            }, 1000);
        }
    });
}

// 切换语言
function changeLanguage(languageCode) {
    return new Promise((resolve, reject) => {
        if (currentLanguageCode === languageCode) {
            resolve();
            return;
        }
        switch (languageCode) {
            case 'zh_HK':
                setLocalStorage('currentLanguageCode', 'zh_HK');
                currentLanguageCode = 'zh_HK';
                lang.dynamic('zh_HK', `lang/zh_HK.json`);
                lang.change('zh_HK', undefined, null);
                break;
            default:
                // 默认语言为英文
                setLocalStorage('currentLanguageCode', 'en_US');
                currentLanguageCode = 'en_US';
                lang.change('en_US', undefined, null);
                break;
        }
        window.location.reload();
        resolve();
    });
}

/**
 * 初始化下拉上拉插件
 * @param {*} downCallback 下拉刷新回调
 * @param {*} downInitedCallback 下拉刷新初始化回调
 * @param {*} upCallback 上拉加载回调
 * @param {*} pageNumber 当前页码
 * @param {*} pageSize 分页数据条数
 * @param {*} upInitedCallback 上拉加载初始化回调
 * @returns 
 */
function initMeScroll(downCallback = null, downInitedCallback = null, upCallback = null, pageNumber = 0, pageSize = 10, upInitedCallback = null) {
    var meScrollJson = {};
    if (isFunctionDefined(downCallback)) {
        meScrollJson.down = {
            callback: downCallback, // 下拉刷新回调
            auto: false, // 自动加载
            textInOffset: window.lang.translate('mescrollTextInOffset'), // 下拉刷新文本
            textOutOffset: window.lang.translate('mescrollTextOutOffset'), // 释放刷新文本
            textLoading: window.lang.translate('mescrollTextLoading') // 加载中文本
        };
        if (isFunctionDefined(downInitedCallback)) {
            meScrollJson.down.inited = downInitedCallback; // 下拉刷新初始化回调
        }
    }
    if (isFunctionDefined(upCallback)) {
        meScrollJson.up = {
            callback: upCallback, // 上拉加载回调
            auto: false, // 自动加载
            noMoreSize: 0, // 列表的总数量要大于noMoreSize条才显示无更多数据，设置为0，加载到最后一页始终显示无更多数据
            empty: { // 无数据时显示
                warpId: null,
                icon: null,
                tip: window.lang.translate('mescrollEmptyTip'),
                btntext: "",
                btnClick: null,
                supportTap: false
            },
            htmlLoading: `<p class="upwarp-progress mescroll-rotate"></p><p class="upwarp-tip">${window.lang.translate('mescrollHtmlLoading')}</p>`, // 加载中
            htmlNodata: `<p class="upwarp-nodata">${window.lang.translate('mescrollHtmlNodata')}</p>`, // 加载到最后一页无数据
            page: { // 分页
                num: pageNumber,
                size: pageSize,
            },
            lazyLoad: { // 懒加载
                use: true,
            }
        };
        if (isFunctionDefined(upInitedCallback)) {
            meScrollJson.up.inited = upInitedCallback; // 上拉加载初始化回调
        }
    }
    return new MeScroll('mescroll', meScrollJson);
}

// 获取当前位置
function getLocation() {
    var longitude = null; // 经度
    var latitude = null; // 纬度
    return new Promise((resolve, reject) => {
        if (navigator.geolocation) {
            navigator.geolocation.getCurrentPosition(
                // 成功
                function (position) {
                    longitude = position.coords.longitude; // 經度
                    latitude = position.coords.latitude; // 緯度
                    setLocalStorage('currentLongitude', longitude);
                    setLocalStorage('currentLatitude', latitude);
                    resolve({ longitude, latitude }); // 解析Promise，表示位置获取成功
                },
                // 失敗
                function (error) {
                    console.log("Failed to obtain location");
                    console.log(error);
                    Swal.fire({
                        position: "top-end",
                        icon: "error",
                        title: window.lang.translate('locationGetFailed'),
                        showConfirmButton: false,
                        timer: 5000
                    });
                    reject(error); // 拒绝Promise，表示位置获取失败
                }
            );
        } else {
            console.log("Geolocation is not supported by this browser.");
            Swal.fire({
                position: "top-end",
                icon: "warning",
                title: window.lang.translate('locationGetFailedBrowser'),
                showConfirmButton: false,
                timer: 5000
            });
            reject(new Error("Geolocation is not supported by this browser.")); // 拒绝Promise
        }
    });
}

// 打开底部菜单
function openBottomMenu(bottomMenuElement = ".bottom-menu") {
    $("#mask").show();
    $(bottomMenuElement).addClass('show');
}

// 关闭底部菜单
function closeBottomMenu(bottomMenuElement = ".bottom-menu") {
    $("#mask").hide();
    $(bottomMenuElement).removeClass('show');
}

// 生成唯一ID
function generateUniqueId() {
    // return crypto.randomUUID();
    return 1234;
}