<!DOCTYPE html>
<html lang="en">

<head>
	<meta charset="utf-8">
	<meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
	<title></title>
	<link href="css/bootstrap.min.css" rel="stylesheet">
	<link href="css/font-awesome.min.css" rel="stylesheet">
	<link href="css/bootstrap-slider.min.css" rel="stylesheet">
	<script type="text/javascript">
		document.write('<link rel="stylesheet" href="css/style.css?time=' + new Date().getTime() + '">');
	</script>
	<script src="js/jquery-3.7.1.min.js"></script>
	<script src="js/jquery-lang.js" charset="utf-8" type="text/javascript"></script>
	<script src="js/sweetalert2.all.min.js"></script>
	<script src="js/big.min.js"></script>
	<script src="js/common/common.js"></script>
</head>

<body>

	<!-- 顶部栏 -->
	<header class="header">
		<button class="back-button">
			<a href="choose_plug.html" class="back-icon">×</a>
		</button>
		<div class="header-title"><i class="fa fa-bolt" aria-hidden="true"></i> </div>

		<div class="language-button">
			<button class="refresh-button">
				<a href="javascript:void(0)" onclick="location.reload()"><i class="fa fa-refresh" aria-hidden="true"></i></a>
			</button>
			<div class="dropdown">

				<button class="btn btn-lang dropdown-toggle" type="button" id="langDropdown" data-bs-toggle="dropdown" aria-expanded="false">
					<i class="fa fa-globe" aria-hidden="true"></i>
				</button>
				<ul class="dropdown-menu dropdown-menu-end" aria-labelledby="langDropdown">
					<li><a class="dropdown-item" href="javascript:void(0);" data-lang="en_US" onclick="changeLanguage('en_US')">English</a></li>
					<li><a class="dropdown-item" href="javascript:void(0);" data-lang="zh_HK" onclick="changeLanguage('zh_HK')">繁體中文</a></li>
				</ul>
			</div>
		</div>
	</header>

	<div class="content charging_content">
		<span class="status_time d-flex justify-content-end"></span>
		<div class="status text-center">
			<img src="img/charging_flash.gif" class="img-responsive center-block">
		</div>

		<div class="status text-center charging"><b lang="en_US">charging</b></div>
		<div class="container ">
			<div class="row info_card">
				<div class="col-6">
					<div class="block time d-flex flex-column align-items-center">
						<div class="mt-auto"></div>
						<div class="time_content"></div>
						<div class="time_title text-center mt-auto" lang="en_US">chargedTime</div>
						<div class="mb-auto"></div>
					</div>
				</div>
				<div class="col-6">
					<div class="block energy d-flex flex-column align-items-center">
						<div class="mt-auto"></div>
						<div class="energy_content"></div>
						<div class="time_title text-center mt-auto" lang="en_US">chargedEnergy</div>
						<div class="mb-auto"></div>
					</div>
				</div>
			</div>

			<div class="row info_card">
				<div class="col-6">
					<div class="block meter_value power d-flex flex-column align-items-center">
						<div class="mt-auto"></div>
						<div class="meter_value_content"></div>
						<div class="time_title text-center mt-auto" lang="en_US">power</div>
						<div class="mb-auto"></div>
					</div>
				</div>
				<div class="col-6">
					<div class="block meter_value current d-flex flex-column align-items-center">
						<div class="mt-auto"></div>
						<div class="meter_value_content"></div>
						<div class="time_title text-center mt-auto" lang="en_US">current</div>
						<div class="mb-auto"></div>
					</div>
				</div>
			</div>

			<div class="row info_card">
				<div class="col-6">
					<div class="block meter_value voltage d-flex flex-column align-items-center">
						<div class="mt-auto"></div>
						<div class="meter_value_content"></div>
						<div class="time_title text-center mt-auto" lang="en_US">voltage</div>
						<div class="mb-auto"></div>
					</div>
				</div>

			</div>

		</div>

		<div class="fixed_content">
			<div class="d-flex justify-content-center align-items-center hide" id="goPaymentButton">
				<button class="btn btn-outline-danger btn-lg px-5" onclick="goPayment()" lang="en_US">btnGoPayment</button>
			</div>
			<!-- 卡片容器 -->
			<div class="card-container est_card price_card hide">
				<!-- 卡片 -->
				<div class="card">
					<div class="card-body">
						<div class="row">
							<div class="col-8">
								<span class="title" lang="en_US">estimatedTotal</span><br>
								<span class="price"></span>
							</div>
							<div class="col-4">
								<a href="javascript:void(0)" onclick="getChargeInfo(false)" class="float-end">
									<i class="fa fa-refresh" aria-hidden="true"></i>
								</a>
							</div>
						</div>
					</div>
				</div>
			</div>

			<!-- 进度条 -->
			<div class="slider-container hide">
				<div class="slider-info" id="sliderInfo" lang="en_US">sliderStopInfo</div>
				<input id="chargeSlider" type="text" class="w-100" data-slider-min="0" data-slider-max="100" data-slider-value="0" data-slider-step="1">
			</div>
		</div>
	</div>

	<!-- 页面加载动画 -->
	<div id="pageLoadingOverlay">
		<div class="charging-container">
			<i class="fa fa-car car-icon"></i>
			<div class="loading-text"></div>
		</div>
	</div>

	<!-- 加载动画 -->
	<div id="loadingOverlay">
		<div class="loading-spinner"></div>
		<div class="loading-text"></div>
	</div>

</body>

<script src="js/bootstrap.bundle.min.js"></script>
<script src="js/bootstrap-slider.min.js"></script>
<script>
	var currentSiteNumber = getLocalStorage('site'); // 场地编号
	var currentConnectorNumber = getLocalStorage('connector'); // 充电枪编号
	var connectorInfo = null; // 充电枪信息
	var currentChargeRecordNumber = null; // 充电记录编号
	var chargeRecord = null; // 充电记录
	var estimatedChargeValueAmount = null; // 预计充电金额
	var estimatedIdlingPenaltyAmount = null; // 预计闲置罚款金额
	var lastUrl = 'choose_plug.html';
	var nextUrl = 'stop_charging_auth.html';

	$(document).ready(function () {
		// 初始化语言包，默认会回调pageReady函数(如存在)
		initLang();
	});

	// 页面初始化
	function pageReady() {
		// 如果为空，则跳转至choose_plug.html
		if (isEmptyString(currentSiteNumber) || isEmptyString(currentConnectorNumber)) {
			window.location.href = `choose_plug.html?site=${currentSiteNumber || getQueryVariable('site', '')}`;
			return;
		}
		if (!isLocalStorageAvailable()) {
			lastUrl += `?site=${currentSiteNumber}&connector=${currentConnectorNumber}`;
			nextUrl += `?site=${currentSiteNumber}&connector=${currentConnectorNumber}`;
		}
		// 获取充电枪信息
		getConnector(currentConnectorNumber).then(() => {
			getChargeInfo();
		});

		// 初始化滑块
		const slider = new Slider('#chargeSlider', {
			formatter: function (value) {
				return value > 70 ? window.lang.translate('sliderStopInfo') : window.lang.translate('sliderStopInfo');
			},
			handle: 'round',
			tooltip: 'hide' // 隐藏默认提示
		});

		// 滑块提示文本元素
		const sliderInfo = document.getElementById('sliderInfo');

		// 加载指示器
		const loadingIndicator = document.getElementById('loadingIndicator');

		// 实现回弹效果
		let isSliding = false;

		slider.on('slideStart', function () {
			isSliding = true;
		});

		slider.on('slide', function (value) {
			const sliderEl = document.getElementById('chargeSlider');

			// 更新滑块上方的提示文本
			sliderInfo.textContent = value > 70 ? window.lang.translate('sliderStopInfo') : window.lang.translate('sliderStopInfo');
			sliderInfo.classList.toggle('active', value > 70);

			if (value > 70) {
				slider.setValue(75);
				sliderEl.closest('.slider-container').classList.add('slider-full');
			} else {
				sliderEl.closest('.slider-container').classList.remove('slider-full');
			}
		});

		slider.on('slideStop', function (value) {
			isSliding = false;

			const sliderEl = document.getElementById('chargeSlider');

			if (value > 70) {
				// 达到阈值，保持在100%
				slider.setValue(75);
				sliderEl.closest('.slider-container').classList.add('slider-full');
				sliderInfo.textContent = window.lang.translate('loading');
				sliderInfo.classList.add('active');

				// 模拟加载延迟，然后跳转到新页面
				setTimeout(() => {
					// 替换为实际的跳转URL
					window.location.href = nextUrl;
				}, 200);
			} else {
				// 未达到阈值，回弹到0
				slider.setValue(0);
				sliderEl.closest('.slider-container').classList.remove('slider-full');
				sliderInfo.textContent = window.lang.translate('sliderStopInfo');
				sliderInfo.classList.remove('active');
			}
		});
	}

	// 获取充电枪信息
	function getConnector(connectorNumber) {
		return new Promise((resolve, reject) => {
			ajaxPostRequestJsonAndCallback({
				url: "api/getConnector.php",
				params: {
					language_code: currentLanguageCode,
					connector_number: connectorNumber,
				},
				successCallback: function (data) {
					connectorInfo = data.data;
					$("title").text(connectorInfo.name);
					$(".header > .header-title").append(connectorInfo.name);
					$(".header > .back-button > a").attr("href", lastUrl);
					resolve(connectorInfo);
				},
				errorCallback: function (message) {
					Swal.fire({
						title: window.lang.translate('warning'),
						confirmButtonText: window.lang.translate('btnConfirm'),
						confirmButtonColor: '#54C49C',
						text: message,
						icon: 'error',
						allowOutsideClick: false,
						showCancelButton: true,
						cancelButtonText: window.lang.translate('btnGoBack') // 自定义返回按钮文本
					}).then(function (result) {
						if (result.isConfirmed) {
							// 点击确认按钮，刷新当前页面
							window.location.reload();
						} else if (result.dismiss === Swal.DismissReason.cancel) {
							// 点击返回按钮，跳转到上一页
							window.location.href = lastUrl;
						}
					});
					reject(message);
				}
			});
		})
	}

	// 获取充电信息
	function getChargeInfo(isAuto = true) {
		var refreshBtn, refreshBtnIcon;
		// 非自动刷新，显示图标旋转动画
		if (!isAuto) {
			// 获取刷新按钮
			refreshBtn = $('.price_card .card-body > .row > .col-4 > a');
			refreshBtnIcon = refreshBtn.find('i');
			if (refreshBtnIcon.hasClass('fa-spin')) {
				return;
			}
			refreshBtn.prop('disabled', true);
			// 添加旋转动画
			refreshBtnIcon.addClass('fa-spin');
		}
		// 获取最新充电相关信息
		getConnectorCurrentChargeRecordNumber(currentConnectorNumber).then(() => {
			if (isAuto) {
				getChargeRecord(currentConnectorNumber).then((chargeRecord) => {
					// getChargeRecordEstimatedAmount(currentConnectorNumber);
					if (!isArrayAndNotEmpty(chargeRecord?.charge_payment_record_list)) {
						// 显示支付按钮
						$("#goPaymentButton").removeClass('hide');
						checkChargeRecordChargePaymentIntentRecord().catch(() => {
							createChargeRecordChargePaymentIntentRecord();
						});
					}
				});
			} else {
				// 手动获取只获取预计金额
				// getChargeRecordEstimatedAmount(currentConnectorNumber);
			}
		}).finally(() => {
			if (!isAuto) {
				// 延迟1秒后关闭旋转动画
				setTimeout(function () {
					refreshBtnIcon.removeClass('fa-spin');
					refreshBtn.prop('disabled', false);
				}, 1000);
			}
		});
	}

	// 获取充电枪当前充电记录编号
	function getConnectorCurrentChargeRecordNumber(connectorNumber) {
		return new Promise((resolve, reject) => {
			ajaxPostRequestJsonAndCallback({
				url: "api/getConnectorCurrentChargeRecordNumber.php",
				params: {
					language_code: currentLanguageCode,
					connector_number: connectorNumber,
				},
				successCallback: function (data) {
					if (!isEmptyString(data.data)) {
						currentChargeRecordNumber = data.data;
						resolve(data);
					} else {
						Swal.fire({
							title: data?.message || window.lang.translate('warning'),
							text: window.lang.translate('noChargeData'),
							icon: 'warning',
							allowOutsideClick: false,
							showCancelButton: true,
							confirmButtonText: window.lang.translate('btnRetry'),
							confirmButtonColor: '#54C49C',
							cancelButtonText: window.lang.translate('cancel'),
						}).then(function (result) {
							if (result.isConfirmed) {
								getChargeInfo();
							} else {
								// 跳回充电枪列表页
								window.location.href = lastUrl;
							}
						});
						reject(data);
					}
				},
				errorCallback: function (message) {
					Swal.fire({
						title: message || window.lang.translate('warning'),
						text: window.lang.translate('getChargeInfoFailed'),
						icon: 'warning',
						allowOutsideClick: false,
						showCancelButton: true,
						confirmButtonText: window.lang.translate('btnRetry'),
						confirmButtonColor: '#54C49C',
						cancelButtonText: window.lang.translate('btnBack') // 自定义返回按钮文本
					}).then(function (result) {
						if (result.isConfirmed) {
							getChargeInfo();
						} else {
							// 跳回充电枪列表页
							window.location.href = lastUrl;
						}
					});
					reject(message);
				}
			});
		})
	}

	// 获取充电记录
	function getChargeRecord(connectorNumber) {
		return new Promise((resolve, reject) => {
			ajaxPostRequestJsonAndCallback({
				url: "api/getChargeRecord.php",
				params: {
					language_code: currentLanguageCode,
					charge_record_number: currentChargeRecordNumber,
				},
				successCallback: function (data) {
					if (isVariableDefined(data.data)) {
						chargeRecord = data.data;
						updatePageChargeInfo();
						resolve(data.data);
					} else {
						Swal.fire({
							title: data?.message || window.lang.translate('warning'),
							text: window.lang.translate('getChargeInfoFailed'),
							icon: 'warning',
							allowOutsideClick: false,
							showCancelButton: true,
							confirmButtonText: window.lang.translate('btnRetry'),
							confirmButtonColor: '#54C49C',
							cancelButtonText: window.lang.translate('cancel'),
						}).then(function (result) {
							if (result.isConfirmed) {
								getChargeInfo();
							} else {
								// 刷新页面
								window.location.reload();
							}
						});
						reject(data);
					}
				},
				errorCallback: function (message) {
					Swal.fire({
						title: message || window.lang.translate('warning'),
						text: window.lang.translate('getChargeInfoFailed'),
						icon: 'warning',
						allowOutsideClick: false,
						showCancelButton: true,
						confirmButtonText: window.lang.translate('btnRetry'),
						confirmButtonColor: '#54C49C',
						cancelButtonText: window.lang.translate('cancel') // 自定义返回按钮文本
					}).then(function (result) {
						if (result.isConfirmed) {
							getChargeInfo();
						} else {
							// 刷新页面
							window.location.reload();
						}
					});
					reject(message);
				}
			});
		})
	}

	// 更新页面充电信息
	function updatePageChargeInfo() {
		// 更新状态时间
		if (!isEmptyString(chargeRecord.gmt_modified)) {
			const statusTime = new Date(chargeRecord.gmt_modified);
			var areaCode = 'en-GB';
			if (currentLanguageCode !== defaultLanguageCode) {
				areaCode = currentLanguageCode.replace('_', '-');
			}
			// 将当前语言代码的下划线替换为中线，再调用Intl本地化日期
			const formattedTime = new Intl.DateTimeFormat(areaCode, {
				day: 'numeric',
				month: 'long',
				hour: '2-digit',
				minute: '2-digit',
				hour12: false
			}).format(statusTime);
			$(".status_time").text(window.lang.translate('updatedAt') + ' ' + formattedTime);
		}
		// 更新时间
		if (!isEmptyString(chargeRecord.charged_time)) {
			// 使用big库计算秒转换为分，向上取整，不足一分钟算一分钟
			const chargedTime = new Big(chargeRecord.charged_time).div(60).round(0, Big.roundUp).toNumber();
			// 拆分小时和分钟
			const hours = Math.floor(chargedTime / 60);
			const minutes = chargedTime % 60;
			$(".time_content").text(hours + window.lang.translate('unitH') + ' ' + minutes + window.lang.translate('unitM'));
		} else {
			$(".time_content").text('—/—');
		}
		// 更新电量
		if (!isEmptyString(chargeRecord.charged_energy)) {
			// 瓦转换为千瓦时，最多保留三位小数
			const chargedEnergy = new Big(chargeRecord.charged_energy).div(1000).toString();
			$(".energy_content").text(chargedEnergy + window.lang.translate('unitKWh'));
		} else {
			$(".energy_content").text('—/—');
		}
		// 更新功率
		// 功率 = meter_value_voltage * meter_value_current / 1000
		const power = new Big(chargeRecord.meter_value_voltage || 0).mul(chargeRecord.meter_value_current || 0).div(1000).toString();
		$(".power .meter_value_content").text(power + window.lang.translate('unitKW'));

		// 更新电流
		$(".current .meter_value_content").text((chargeRecord.meter_value_current || '0.00') + window.lang.translate('unitA'));

		// 更新电压
		$(".voltage .meter_value_content").text((chargeRecord.meter_value_voltage || '0.00') + window.lang.translate('unitV'));
	}

	// 检查充电记录充点支付意图记录
	function checkChargeRecordChargePaymentIntentRecord() {
		return new Promise((resolve, reject) => {
			ajaxPostRequestJsonAndCallback({
				url: "api/checkChargeRecordChargePaymentIntentRecord.php",
				params: {
					language_code: currentLanguageCode,
					charge_record_number: currentChargeRecordNumber,
				},
				successCallback: function (data) {
					if (isObjectPropertyDefined(data.data, "online_payment_link_url")) {
						window.location.href = "api/redirect.php?checkUrl=" + encodeURIComponent(data.data.online_payment_link_url);
						resolve(data);
					} else {
						reject(data);
					}
				},
				errorCallback: function (message) {
					Swal.fire({
						title: message || window.lang.translate('warning'),
						text: window.lang.translate('requestFailed'),
						icon: 'warning',
						allowOutsideClick: false,
						showCancelButton: true,
						confirmButtonText: window.lang.translate('btnRetry'),
						confirmButtonColor: '#54C49C',
						cancelButtonText: window.lang.translate('cancel') // 自定义返回按钮文本
					}).then(function (result) {
						if (result.isConfirmed) {
							getChargeInfo();
						} else {
							// 刷新页面
							window.location.reload();
						}
					});
				}
			});
		})
	}

	// 创建充电记录充点支付意图记录
	function createChargeRecordChargePaymentIntentRecord() {
		return new Promise((resolve, reject) => {
			ajaxPostRequestJsonAndCallback({
				url: "api/createChargeRecordChargePaymentIntentRecord.php",
				params: {
					language_code: currentLanguageCode,
					charge_record_number: currentChargeRecordNumber,
				},
				successCallback: function (data) {
					if (isObjectPropertyDefined(data.data, "online_payment_link_url")) {
						window.location.href = "api/redirect.php?checkUrl=" + encodeURIComponent(data.data.online_payment_link_url);
						resolve(data);
					} else {
						reject(data);
					}
				},
				errorCallback: function (message) {
					Swal.fire({
						title: message || window.lang.translate('warning'),
						text: window.lang.translate('requestFailed'),
						icon: 'warning',
						allowOutsideClick: false,
						showCancelButton: true,
						confirmButtonText: window.lang.translate('btnRetry'),
						confirmButtonColor: '#54C49C',
						cancelButtonText: window.lang.translate('cancel') // 自定义返回按钮文本
					}).then(function (result) {
						if (result.isConfirmed) {
							getChargeInfo();
						} else {
							// 刷新页面
							window.location.reload();
						}
					});
				}
			});
		})
	}

	// 获取充电记录预计金额
	function getChargeRecordEstimatedAmount(connectorNumber) {
		return new Promise((resolve, reject) => {
			ajaxPostRequestJsonAndCallback({
				url: "api/getChargeRecordEstimatedAmount.php",
				params: {
					language_code: currentLanguageCode,
					charge_record_number: currentChargeRecordNumber,
				},
				successCallback: function (data) {
					if (isVariableDefined(data.data)) {
						if (!isEmptyString(data.data.charge_value_amount)) {
							estimatedChargeValueAmount = data.data.charge_value_amount;
						}
						if (!isEmptyString(data.data.idling_penalty_amount)) {
							estimatedIdlingPenaltyAmount = data.data.idling_penalty_amount;
						}
						// 更新页面上的金额
						updateEstimatedAmount();
						resolve(data);
					} else {
						Swal.fire({
							title: data?.message || window.lang.translate('warning'),
							text: window.lang.translate('getChargeInfoFailed'),
							icon: 'warning',
							allowOutsideClick: false,
							showCancelButton: true,
							confirmButtonText: window.lang.translate('btnRetry'),
							confirmButtonColor: '#54C49C',
							cancelButtonText: window.lang.translate('cancel'),
						}).then(function (result) {
							if (result.isConfirmed) {
								getChargeInfo();
							}
						});
						reject(data);
					}
				},
				errorCallback: function (message) {
					Swal.fire({
						title: message || window.lang.translate('warning'),
						text: window.lang.translate('getChargeInfoFailed'),
						icon: 'warning',
						allowOutsideClick: false,
						showCancelButton: true,
						confirmButtonText: window.lang.translate('btnRetry'),
						confirmButtonColor: '#54C49C',
						cancelButtonText: window.lang.translate('cancel') // 自定义返回按钮文本
					}).then(function (result) {
						if (result.isConfirmed) {
							getChargeInfo();
						}
					});
					reject(message);
				}
			});
		})
	}

	// 更新页面上的金额
	function updateEstimatedAmount() {
		var currency = window.lang.translate('unitHKD');
		if (!isEmptyString(chargeRecord.currency_symbol)) {
			currency = chargeRecord.currency_symbol;
		}
		// 更新总金额
		var totalAmount = new Big(estimatedChargeValueAmount || 0).plus(estimatedIdlingPenaltyAmount || 0).toFixed(1);
		// 分转元，保留一位小数
		totalAmount = new Big(totalAmount).div(100).toFixed(1);
		$(".price_card .price").html(`<b>${currency}</b> ${totalAmount}`);
	}

	function goPayment() {
		getChargeInfo();
	}

</script>


</html>
