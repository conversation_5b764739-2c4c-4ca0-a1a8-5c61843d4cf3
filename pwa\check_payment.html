<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <title lang="en_US">webTitleCheckPayment</title>></title>
    <link href="css/bootstrap.min.css" rel="stylesheet">
    <link href="css/font-awesome.min.css" rel="stylesheet">
    <script type="text/javascript">
        document.write('<link rel="stylesheet" href="css/style.css?time=' + new Date().getTime() + '">');
    </script>
    <script src="js/jquery-3.7.1.min.js"></script>
    <script src="js/jquery-lang.js" charset="utf-8" type="text/javascript"></script>
    <script src="js/sweetalert2.all.min.js"></script>
    <script src="js/common/common.js"></script>
</head>

<body>

    <!-- 顶部栏 -->
    <header class="header">
        <button class="back-button">
            <a href="charging.html" class="back-icon"><i class="fa fa-angle-left"></i></a>
        </button>
        <div class="header-title"></div>
        <div class="language-button">
            <div class="dropdown">
                <button class="btn btn-lang dropdown-toggle" type="button" id="langDropdown" data-bs-toggle="dropdown" aria-expanded="false">
                    <i class="fa fa-globe" aria-hidden="true"></i>
                </button>
                <ul class="dropdown-menu dropdown-menu-end" aria-labelledby="langDropdown">
                    <li><a class="dropdown-item" href="javascript:void(0);" data-lang="en_US" onclick="changeLanguage('en_US')">English</a></li>
                    <li><a class="dropdown-item" href="javascript:void(0);" data-lang="zh_HK" onclick="changeLanguage('zh_HK')">繁體中文</a></li>
                </ul>
            </div>
        </div>
    </header>

    <div class="d-flex justify-content-center align-items-center text-center" style="min-height: 90vh;">
        <div class="payment-card col-12 col-sm-10 col-md-8 col-lg-6 col-xl-5">
            <h2 class="h2 mb-3 fw-bold" lang="en_US">checkPaymentText</h2>
            <button id="checkBtn" class="btn btn-check-custom fw-bold" onclick="checkPayment()" lang="en_US">btnCheckPaymentResult</button>
        </div>
    </div>

    <!-- 页面加载动画 -->
    <div id="pageLoadingOverlay">
        <div class="charging-container">
            <i class="fa fa-car car-icon"></i>
            <div class="loading-text"></div>
        </div>
    </div>

    <!-- 加载动画 -->
    <div id="loadingOverlay">
        <div class="loading-spinner"></div>
        <div class="loading-text"></div>
    </div>

</body>
<script>
    var lastUrl = 'charging.html';
    var nextUrl = 'charging.html';

    $(document).ready(function () {
        // 初始化语言包，默认会回调pageReady函数(如存在)
        initLang();
    });

    // 页面初始化
    function pageReady() {
        // 如果依旧为空，则跳转至choose_plug.html
        /* if (isEmptyString(currentSiteNumber) || isEmptyString(currentConnectorNumber) || isEmptyString(settingToken)) {
            window.location.href = `choose_plug.html?site=${currentSiteNumber || getQueryVariable('site', '')}`;
            return;
        }
        if (!isLocalStorageAvailable()) {
            lastUrl += `?site=${currentSiteNumber}`;
            nextUrl += `?site=${currentSiteNumber}&connector=${currentConnectorNumber}`;
        } */
        

    }

    function checkPayment() {
        window.location.href = nextUrl;
    }

</script>

</html>
