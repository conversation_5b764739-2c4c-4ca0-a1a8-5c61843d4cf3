<?php
ini_set("display_errors", "On"); //打开错误提示
ini_set("error_reporting", E_ALL); //显示所有错误
date_default_timezone_set('Asia/Hong_Kong');
include_once(__DIR__ . '/config.php');

function dump($data)
{
    echo '<pre>';
    var_dump($data);
    echo '</pre>';
}

function dd($data)
{
    dump($data);
    die;
}

// 写入日志
function writeLog($message = null): void
{
    if (is_null($message))
        return;
    if (is_array($message))
        $message = json($message);
    // 获取当前请求网址
    $url = $_SERVER['REQUEST_URI'];
    $message = date('Y-m-d H:i:s') . ' URL: ' . $url . ' ' . $message . PHP_EOL;
    // 按照日期分文件
    $filename = date('Y-m-d') . '.log';
    $filename = __DIR__ . '/../log/' . $filename;
    file_put_contents($filename, $message, FILE_APPEND);
}

// 数组转json, 中文不编码为unicode
function json(array $data = array()): string
{
    return json_encode($data, JSON_UNESCAPED_UNICODE);
}

function returnJson($json = array()): void
{
    //声明header为json
    header("Content-type:application/json");
    echo json($json);
    exit;
}

function input($data)
{
    if (is_null($data)) {
        return null;
    }
    $data = trim($data);
    $data = stripslashes($data);
    $data = htmlspecialchars($data);
    return safe_replace($data);
}

/**
 * 安全过滤函数
 *
 * @param $string
 * @return string
 */
function safe_replace($string)
{
    $string = str_replace('%20', '', $string);
    $string = str_replace('%27', '', $string);
    $string = str_replace('%2527', '', $string);
    $string = str_replace('*', '', $string);
    $string = str_replace('"', '&quot;', $string);
    $string = str_replace("'", '', $string);
    $string = str_replace('"', '', $string);
    $string = str_replace(';', '', $string);
    $string = str_replace('<', '&lt;', $string);
    $string = str_replace('>', '&gt;', $string);
    $string = str_replace("{", '', $string);
    $string = str_replace('}', '', $string);
    $string = str_replace('\\', '', $string);
    return $string;
}

function get($key, $default = null)
{
    return isset($_GET[$key]) ? input($_GET[$key]) : $default;
}

function post($key, $default = null)
{
    return isset($_POST[$key]) ? input($_POST[$key]) : $default;
}

function session($key, $default = null)
{
    return isset($_SESSION['data'][$key]) ? $_SESSION['data'][$key] : $default;
}

function setSession($key, $value = null)
{
    $_SESSION['data'][$key] = $value;
}

/**
 * curl post
 *
 * @param string $url
 * @param mixed $post_data
 * @param array $headers
 * @return string
 * @Description
 * @example
 * <AUTHOR>
 * @date 2022-02-23
 */
function curlPost(string $url, $post_data = null, array $headers = []): string
{
    if (MERCHANT_NUMBER !== null) {
        $post_data['merchant_number'] = MERCHANT_NUMBER;
    }
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
    // post数据
    curl_setopt($ch, CURLOPT_POST, 1);
    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, FALSE);
    curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, FALSE);
    // 头部
    /* if (!empty($headers)) {
        curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
    } */
    // Header 添加 Accept-Language
    $headers[] = 'Accept-Language: ' . ($post_data['language_code'] ?? DEFAULT_LANGUAGE_CODE);
    if (isset($post_data['language_code'])) {
        unset($post_data['language_code']);
    }
    curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);

    /* curl_setopt($ch, CURLOPT_HTTPAUTH, CURLAUTH_BASIC);
    curl_setopt($ch, CURLOPT_USERPWD, "$username:$password"); */
    curl_setopt($ch, CURLOPT_POSTFIELDS, $post_data);
    $output = curl_exec($ch);
    $err = curl_error($ch);
    /* $status_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    echo($status_code); */
    curl_close($ch);
    //打印获得的数据

    return !empty($err) ? $err : $output;
    // return $output;
}

/**
 * curl get
 *
 * @param string $url
 * @return string
 * @Description
 * @example
 * <AUTHOR>
 * @date 2022-02-23
 */
function curlGet(string $url): string
{
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
    // post数据
    // curl_setopt($ch, CURLOPT_POST, 1);
    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, FALSE);
    curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, FALSE);
    /* curl_setopt($ch, CURLOPT_HTTPAUTH, CURLAUTH_BASIC);
    curl_setopt($ch, CURLOPT_USERPWD, "$username:$password"); */
    // curl_setopt($ch, CURLOPT_POSTFIELDS, http_build_query($post_data));
    $output = curl_exec($ch);
    $err = curl_error($ch);
    /* $status_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    echo($status_code); */
    curl_close($ch);
    //打印获得的数据

    return !empty($err) ? $err : $output;
    // return $output;
}
