/* 强制所有元素使用亮色模式 */
:root {
  color-scheme: light;
}

/* 针对不同浏览器的兼容写法 */
@media (prefers-color-scheme: dark) {

  /* 当系统为暗色模式时，强制使用亮色主题 */
  body,
  html {
    background-color: white !important;
  }

  /* 强制所有元素使用亮色模式的颜色 */
  * {
    color-scheme: light;
  }
}

.hide {
  display: none !important;
}

:lang(zh_HK) .ls-1 {
  letter-spacing: 0.1em;
}

:lang(zh_HK) .ls-2 {
  letter-spacing: 0.2em;
}

/** 下拉范围 **/
.scroll_box {
  position: fixed;
  height: auto;
}

/* 加载动画样式 */
#loadingOverlay {
  display: none;
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 1000;
  justify-content: center;
  align-items: center;
  flex-direction: column;
}

#loadingOverlay .loading-spinner {
  width: 50px;
  height: 50px;
  border: 5px solid rgba(255, 255, 255, 0.3);
  border-radius: 50%;
  border-top-color: #007bff;
  animation: spin 1s ease-in-out infinite;
  margin-bottom: 15px;
}

#loadingOverlay .loading-text {
  color: white;
  font-size: 18px;
  text-align: center;
}

/* 动画关键帧 */
@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

/* 页面加载动画 */
#pageLoadingOverlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: white;
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 9999;
  transition: opacity 0.5s ease-in-out;
}

/* 加载动画容器 */
#pageLoadingOverlay .charging-container {
  width: 200px;
  height: 150px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

/* 电车图标 */
#pageLoadingOverlay .car-icon {
  font-size: 60px;
  color: #4CBF37;
  animation: fade-in 1.5s ease-in-out;
}

/* 文字提示 */
#pageLoadingOverlay .loading-text {
  margin-top: 20px;
  font-family: 'Arial', sans-serif;
  font-size: 16px;
  color: #2c3e50;
  text-align: center;
  animation: fade-in 1.5s ease-in-out;
}

/* 淡入动画 */
@keyframes fade-in {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }

  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 响应式调整 */
@media (max-width: 600px) {
  .loading-spinner {
    width: 40px;
    height: 40px;
    border-width: 4px;
  }

  .loading-text {
    font-size: 16px;
  }
}

.bd-placeholder-img {
  font-size: 1.125rem;
  text-anchor: middle;
  -webkit-user-select: none;
  -moz-user-select: none;
  user-select: none;
}

@media (min-width: 768px) {
  .bd-placeholder-img-lg {
    font-size: 3.5rem;
  }
}

.b-example-divider {
  height: 3rem;
  background-color: rgba(0, 0, 0, 0.1);
  border: solid rgba(0, 0, 0, 0.15);
  border-width: 1px 0;
  box-shadow: inset 0 0.5em 1.5em rgba(0, 0, 0, 0.1),
    inset 0 0.125em 0.5em rgba(0, 0, 0, 0.15);
}

.b-example-vr {
  flex-shrink: 0;
  width: 1.5rem;
  height: 100vh;
}

.bi {
  vertical-align: -0.125em;
  fill: currentColor;
}

.nav-scroller {
  position: relative;
  z-index: 2;
  height: 2.75rem;
  overflow-y: hidden;
}

.nav-scroller .nav {
  display: flex;
  flex-wrap: nowrap;
  padding-bottom: 1rem;
  margin-top: -1px;
  overflow-x: auto;
  text-align: center;
  white-space: nowrap;
  -webkit-overflow-scrolling: touch;
}

body {
  background-color: #fefefe;
  margin: 0;
  padding: 0;
  font-family: 'Roboto Condensed', '苹方', '微軟正黑體', '微軟雅黑體',
    '黑体', 'Helvetica', 'Arial', sans-serif;
  height: auto !important;
}

/* 顶部栏样式 */
.header {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 60px;
  background-color: white;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 100;
}

.header-title {
  font-size: 16px;
  text-align: center;
  color: #333;
}

.back-button {
  position: absolute;
  left: 10px;
  top: 50%;
  transform: translateY(-50%);
  cursor: pointer;
  width: 40px;
  height: 40px;
  background-color: #F1F1F1;
  border-radius: 10px;
  border: none;
}

.back-icon {
  font-size: 24px;
  color: #555;
  line-height: 40px;
  text-decoration: none;
  display: flex;
  justify-content: center;
  align-items: center;
}

.content {
  margin-top: 80px;
  /* 确保内容不被顶部栏遮挡 */
  margin-bottom: 80px;
}

/* 搜索框样式 */
.search-container {
  display: inline-flex;
  border: 1px solid #ccc;
  border-radius: 10px;
  overflow: hidden;
  margin: 20px 0;
  width: 100%;
}

.search-input {
  border: none;
  padding: 8px 12px;
  font-size: 16px;
  outline: none;
  background: transparent;
  width: 100%;
}

.search-button {
  background: transparent;
  border: none;
  padding: 8px 12px;
  cursor: pointer;
  transition: background-color 0.3s;
}

.search-button:hover {
  background-color: transparent;
}

.search-icon {
  color: #555;
  font-size: 16px;
}

.site .search-container {
  width: 80%;
}

.filter-button {
  background-color: #858585;
  border: none;
  border-radius: 10px;
  width: 40px;
  height: 40px;
}

.filter-button.active {
  background-color: #54C49C;
}

.filter-button img {
  margin: 0 auto;
  /*display: flex;
  justify-content: center;
  align-items: center;
  line-height: 40px;*/
}

.filter-container {
  width: 16%;
  display: inline-flex;
  overflow: hidden;
  justify-content: flex-end;
  /* 自身及子元素整体居右（需父元素是 flex 容器） */
}

/* 楼层标签样式 */
.floor_tag {
  position: relative;
}

.floor_tag .tag {
  width: fit-content;
  background-color: #54C49C;
  color: #fff;
  font-size: 14px;
  padding: 8px 50px;
  border-top-right-radius: 20px;
  border-bottom-right-radius: 20px;
  box-shadow: rgba(84, 196, 156, 0.25) 0 2px 6px 2px;
}

.list_box .list-group {
  margin: 20px 0 !important;
}

/* Site 卡片样式 */
.site_card {
  background-color: #F9F9F9 !important;
  border: none !important;
  border-radius: 15px;
  width: 100%;
  height: 100%;
  padding: 15px;
  position: relative;
  margin-bottom: 20px;
}

.site_card .site_img {
  width: 65px;
  height: 78px;
}

.site_img {
  border-radius: 8px;
}

.info-content {
  margin-left: 15px;
}

.info-content h2 {
  font-size: 18px;
  margin-bottom: 5px;
}

.info-content h2 span {
  font-size: 16px;
  color: #333;
  font-weight: normal;
}

.info-content p {
  margin: 6px 0;
  font-size: 14px;
  color: #666;
  display: flex;
  align-items: center;
}

.info-content p span {
  padding-right: 10px;
  color: #54C29B;
}

.icon-search {
  position: absolute;
  top: 20px;
  right: 20px;
  color: #333;
  cursor: pointer;
}

.distance-btn {
  position: absolute;
  bottom: 0;
  right: 0;
  background: linear-gradient(135deg, #54C39C, #00905D);
  color: white;
  padding: 8px 20px;
  border-radius: 15px;
  border-top-right-radius: 0;
  border-bottom-left-radius: 0;
  display: flex;
  font-size: 14px;
  align-items: center;
}

/* 充电桩卡片样式 */
.charging_list .charging_card {
  background-color: #F9F9F9 !important;
  border: none !important;
  border-radius: 15px;
  width: 100%;
  height: 100%;
}

.charging_card {
  position: relative;
}

.charging,
.offline,
.available,
.preparing,
.finishing,
.unavailable,
.queuing,
.faulted {
  position: relative;
}

/* 充电桩状态指示条 */
.charging::before,
.offline::before,
.available::before,
.preparing::before,
.finishing::before,
.queuing::before,
.unavailable::before,
.faulted::before {
  content: '';
  height: 60px;
  width: 2px;
  position: absolute;
  display: block;
  z-index: 1;
  border-radius: 50px;
  top: 50%;
  transform: translate(-50%, -50%);
}

.charging::before {
  background-color: #D44F04;
}

.offline::before {
  background-color: #B5B5BA;
}

.available::before {
  background-color: #00905D;
}

.preparing::before {
  background-color: #306B3C;
}

.finishing::before {
  background-color: #00B9FF;
}

.queuing::before {
  background-color: #025CA5;
}

.unavailable::before {
  background-color: #F61B1A;
}

.faulted::before {
  background-color: #B00404;
}

.charging_card label {
  border: none !important;
  background-color: #F9F9F9 !important;
  border-radius: 15px !important;
}

.charging_card strong {
  font-size: 24px;
  font-weight: normal !important;
  display: inline-block;
}

/* 状态文本颜色 */
.status_charging {
  font-size: 22px;
  color: #D44F04;
}

.status_offline {
  font-size: 22px;
  color: #B5B5BA;
}

.status_available {
  font-size: 22px;
  color: #00905D;
}

.status_preparing {
  font-size: 22px;
  color: #306B3C;
}

.status_finishing {
  font-size: 22px;
  color: #00B9FF;
}

.status_unavailable {
  font-size: 22px;
  color: #FF3D3D;
}

.status_queuing {
  font-size: 22px;
  color: #025CA5;
}

.status_faulted {
  font-size: 22px;
  color: #CC0000;
}

/* 充电卡片其他元素 */
.plug_type {
  margin-top: 10px;
  text-align: right;
  padding-right: 20px;
}

.brand {
  text-align: right;
  padding-right: 20px;
}

.brand img {
  display: inline-block;
}

.text-center {
  text-align: center;
}

/* 充电器标题区域 */
.charger_title {
  position: relative;
}

.charger_title span {
  font-size: 24px;
  color: #54C19A;
  vertical-align: middle;
}

.charger_title .title {
  display: block;
  position: relative;
}

.charger_title img {
  vertical-align: middle;
  padding-right: 5px;
}

.charger_title .back {
  float: left;
  position: absolute;
  left: 0;
  top: -5px;
}

.charger_title .back img {
  padding-right: 0;
  width: 45px;
}

/* 内容区域 */
.content_box {
  margin-top: 20px;
}

.content_box h2 {
  font-size: 62px;
  color: #54C29B;
}

.content_box .text {
  font-size: 18px;
}

/** Site Detail **/
.parking-img img {
  width: 100%;
  max-width: 100%;
}

.parking-img {
  position: relative;
}

.container-fluid {
  padding-left: 0 !important;
  padding-right: 0 !important;
}

.site_detail_back i {
  color: #fff;
  font-size: 24px;
}

.site_detail_back {
  padding: 5px 15px;
  position: absolute;
  top: 10px;
  background-color: rgba(0, 0, 0, 0.42);
  left: 20px;
  border-radius: 10px;
}

.site_detail_content {
  border-top-left-radius: 20px;
  margin-top: -20px;
  border-top-right-radius: 20px;
}

.site_detail_content .nav-tabs {
  border: none;
  border-radius: 10px;
  background-color: #fff;
  box-shadow: 0px 2px 13px 0px rgba(0, 0, 0, 0.1);
}

.site_detail_content .nav-tabs .nav-item.show .nav-link,
.site_detail_content .nav-tabs .nav-link.active {
  border: none !important;
  color: #3BAA82 !important;
}

.site_detail_content .nav-link:focus,
.site_detail_content .nav-link:hover,
.site_detail_content .nav-link {
  color: #AEAEAE;
}

.site_detail_content .nav-tabs .nav-link {
  border: none !important;
  margin-bottom: 0px !important;
}

.site_detail_content .nav-tabs {
  border: none !important;
}

.site_detail_content .tab-content h6 {
  font-size: 18px;
  color: #AEAEAE;
}

.site_detail_content .tab-content p i {
  margin-right: 10px;
  color: #3BAA82;
}

.site_detail_content .tab-content p {
  margin-bottom: 0px;
  font-size: 15px !important;
}

.site_detail_content .tab-content p span {
  color: #3BAA82;
}

.site_detail_content .tab-content p img {
  margin-right: 10px;
  vertical-align: middle;
}

.site_detail_content .tab-content {
  padding: 1.1em 0px;
}

.border-bottom {
  padding-bottom: 10px;
  border-bottom: 1px solid #F3F3F3;
}

.btn-lang {
  background: transparent;
  border: none;
  color: #333;
  font-size: 1.2rem;
}

.btn-lang:hover,
.btn-lang:focus {
  color: #54C49C;
}

.language-button {
  position: absolute;
  right: 10px;
  top: 50%;
  transform: translateY(-50%);
  display: flex;
  align-items: center;
}

.language-button .dropdown-menu>li.active a {
  color: #54C49C;
}

.refresh-button {
  cursor: pointer;
  border-radius: 10px;
  background-color: transparent;
  border: none;
  padding: 0;
}

.refresh-button a {
  display: flex;
  align-items: center;
  justify-content: center;
  text-decoration: none;
  color: inherit;
}

.refresh-button i {
  color: #333;
  font-size: 1.2rem;
}

.price_card {
  margin-bottom: 150px;
}

.price_card .card {
  border-radius: 32px;
  padding: 10px 5px;
  border: none;
  box-shadow: 0px 2px 13px 0px rgba(0, 0, 0, 0.1);
}

.price_card .card-body .title {
  color: #AEAEAE;
  font-size: 16px;
}

.price_card .card-body .price {
  color: #FF8616;
  font-size: 32px;
}

.price_card .card-body .price b {
  font-weight: normal;
  font-size: 18px;
}

.price_card .card-body a i {
  font-size: 24px;
}

.price_card .card-body a {
  margin: 20px 0px;
  color: #000000 !important;
}

.info_card .block {
  background: #fff;
  box-shadow: 0px 1px 2px 1px rgba(0, 0, 0, 0.1);
  border-radius: 25px;
  padding: 13px 30px;
  margin-bottom: 20px;
  height: 120px;
}

.info_card .block .time_content {
  color: #5EDFB1;
  text-align: center;
  font-size: 30px;
}

.info_card .time {
  background-image: url(../img/time_bg.png);
  background-repeat: no-repeat;
  background-position: right;
  background-size: contain;
}

.info_card .energy {
  background-image: url(../img/flash_bg.png);
  background-repeat: no-repeat;
  background-position: right;
  background-size: contain;
}

.info_card .meter_value {
  background-image: url(../img/flash_bg.png);
  background-repeat: no-repeat;
  background-position: right;
  background-size: contain;
}

.info_card .block .energy_content {
  color: #FFB57A;
  text-align: center;
  font-size: 30px;
}

.info_card .block .time_title {
  font-size: 16px;
}

.info_card .block .meter_value_content {
  color: #6286CC;
  text-align: center;
  font-size: 30px;
}

.est_card {
  position: fixed;
  width: 100%;
  margin: 0;
  padding: 10px;
  z-index: 100;
  bottom: 130px;
  left: 50%;
  transform: translateX(-50%);
  background-color: #FFFFFF;
}

.charging_content {
  /* height: 980px; */
  overflow: hidden;
  overflow-y: overlay;
}

/* 自定义滑块样式 */
.slider {
  width: 100%;
  height: 60px;
  position: relative;
}

.slider.slider-horizontal {
  height: 80px;
  width: 100%;
}

.slider-track {
  height: 100%;
  border-radius: 50px;
  background: linear-gradient(90deg, #D2665F 0%, #BE1111 100%);
  box-shadow: inset 0 1px 2px rgba(0, 0, 0, 0.1);
}

.start .slider-track {
  height: 100%;
  border-radius: 50px;
  background: linear-gradient(90deg, #52BE98 0%, #60E3B4 100%);
  box-shadow: inset 0 1px 2px rgba(0, 0, 0, 0.1);
}

.slider-selection {
  background: transparent;
  border-radius: 30px;
}

.slider-handle {
  width: 60px;
  height: 60px;
  margin-top: 0px;
  background-color: #F3A09D;
  background-image: none;
  border: none;
  border-radius: 50% !important;
  transition: all 0.15s ease;
  color: #fff;
  z-index: 2;
}

.start .slider-handle {
  width: 60px;
  height: 60px;
  margin-top: 0px;
  background-color: #8DD7BC;
  background-image: none;
  border: none;
  border-radius: 50% !important;
  transition: all 0.15s ease;
  color: #fff;
  z-index: 2;
}

.slider-handle:hover {
  transform: scale(1.05);
}

.slider-handle:active {
  transform: scale(1.05);
}

.fixed_content {
  position: relative;
  background: white;
  padding: 20px;
  margin: 0 auto;
  max-width: 800px;
}

/* 自定义滑块内图标 */
.slider-handle:before {
  content: '■';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  font-size: 24px;
  color: #fff !important;
}

.start .slider-handle:before {
  content: "\f0e7";
  /* Font Awesome解锁图标的Unicode值 */
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  font-size: 24px;
  font-family: "FontAwesome";
  /* 指定Font Awesome字体 */
  color: #fff !important;
}

/* 滑块提示文本替代方案 */
.slider-info {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translateX(-50%) translateY(-50%);
  font-weight: bold;
  font-size: 15px;
  color: #fff;
  z-index: 1;
  font-weight: normal;
  transition: color 0.3s ease;
}

.slider-info.active {
  color: #fff;
}

.slider.slider-horizontal .slider-track {
  margin: 0;
  top: auto;
  height: 80px;
}

/* 隐藏滑块提示 */
.slider-tooltip {
  display: none !important;
}

.slider.slider-horizontal .slider-tick,
.slider.slider-horizontal .slider-handle {
  margin: 10px;
}

.slider-container {
  position: fixed;
  bottom: 30px;
  width: 90%;
  margin: 0;
  padding: 10px;
  z-index: 100;
  left: 50%;
  transform: translateX(-50%);
}

/* 文字跳动动画 */
.animate-text {
  text-align: center;
  display: inline-block;
  white-space: nowrap;
  font-size: 32px;
  font-weight: bold;
  color: #000;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

/* 弹跳动画关键帧 */
@keyframes bounceIn {

  0%,
  20%,
  50%,
  80%,
  100% {
    transform: translateY(0);
  }

  40% {
    transform: translateY(-15px);
  }

  60% {
    transform: translateY(-7px);
  }
}

/* 字符动画设置 */
.animate-text span {
  display: inline-block;
  animation: bounceIn 1.5s ease infinite;
  animation-fill-mode: both;
}

/* 字符延迟设置（最多支持11个字符） */
.animate-text span:nth-child(1) {
  animation-delay: 0.1s;
}

.animate-text span:nth-child(2) {
  animation-delay: 0.2s;
}

.animate-text span:nth-child(3) {
  animation-delay: 0.3s;
}

.animate-text span:nth-child(4) {
  animation-delay: 0.4s;
}

.animate-text span:nth-child(5) {
  animation-delay: 0.5s;
}

.animate-text span:nth-child(6) {
  animation-delay: 0.6s;
}

.animate-text span:nth-child(7) {
  animation-delay: 0.7s;
}

.animate-text span:nth-child(8) {
  animation-delay: 0.8s;
}

.animate-text span:nth-child(9) {
  animation-delay: 0.9s;
}

.animate-text span:nth-child(10) {
  animation-delay: 1.0s;
}

.animate-text span:nth-child(11) {
  animation-delay: 1.1s;
}

/* 副文本样式 */
.subtext {
  margin-top: 10px;
  color: #333;
  font-size: 16px;
}

.bottom_text {
  position: fixed;
  bottom: 10px;
  color: #333;
  left: 50%;
  white-space: nowrap;
  font-size: 14px;
  transform: translate(-50%, -50%);
}

.status img {
  padding: 40px;
}

.img-responsive {
  max-width: 100%;
}

.center-block {
  display: block;
  margin: 0 auto;
}

.charging_content .status_time {
  font-size: 14px;
  margin-right: 15px;
  color: #AAAAAA;
}

.charging_content .status img {
  padding: 10px 40px 10px 40px;
}

.charging_content .charging b {
  font-size: 35px;
  padding: 10px 0px;
  display: block;
  color: #B85B23;
  font-weight: bold;
}

.charging_content .completed b {
  font-size: 30px;
  padding: 20px 0px;
  display: block;
  color: #1CA172;
  font-weight: bold;
}

.charging_content .charging::before {
  background-color: transparent;
}

.distance {
  color: #32A27A;
  /* 绿色文字，与按钮同色 */
  font-size: 15px;
}

/* 按钮容器（整体布局） */
.navigation-button {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  text-align: center;
  position: relative;
  top: -42px;
}

/* 菱形按钮样式 */
.diamond {
  width: 35px;
  height: 35px;
  background-color: #32A27A;
  /* 绿色背景，可调整色值 */
  transform: rotate(45deg);
  /* 旋转45度形成菱形 */
  display: flex;
  line-height: 35px;
  color: #fff;
  justify-content: center;
  align-items: center;
  margin-bottom: 5px;
  /* 与下方文字的间距 */
  border-radius: 5px;
  /* 轻微圆角（可选，根据设计调整） */
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
  /* 阴影增强立体感 */
}

.diamond i {
  transform: rotate(-45deg);
  margin-right: 2px;
  font-size: 16px;
}

/* 底部导航栏样式 */
.bottom-nav {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: #fff;
  padding: 8px 20px;
  box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.1);
  border-radius: 20px 20px 0 0;
  /* 顶部圆角 */
  display: flex;
  justify-content: space-around;
  /* 均匀分布菜单项 */
  align-items: center;
}

/* 菜单项样式 */
.bottom-nav .nav-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  cursor: pointer;
}

/* 图标样式（可替换为实际图标，此处用Unicode符号示例） */
.bottom-nav img {
  width: 24px;
}

/* 文字标签样式 */
.bottom-nav .label {
  font-size: 14px;
  color: #AEAEAE;
  font-family: Arial, sans-serif;
}

.bottom-nav span {
  display: block;
}

.bottom-nav label {
  text-decoration: none;
}

.nav-item a {
  text-decoration: none;
}

.bottom-nav .active .label {
  color: #2A9B73;
}

.bottom-nav .active img {
  filter: invert(46%) sepia(85%) saturate(328%) hue-rotate(119deg) brightness(95%) contrast(89%);
}

/* Charge Record */
.record_icon {
  display: flex;
  justify-content: space-around;
  /* 均匀分布菜单项 */
  align-items: center;
  border-top: 1px #ececec solid;
  padding-top: 10px;
  margin-top: 10px;
}

.record_icon .item {
  flex-direction: column;
  align-items: center;
  text-align: center;
}

.record_icon i {
  padding-right: 5px;
  font-size: 16px;
}

.record_icon .time {
  color: #00BA78;
}

.record_icon .kwh {
  color: #F6A900;
}

.record_icon .price {
  color: #ED731C;
}

.record_box .info-content p span {
  color: #aeaeae;
  font-size: 16px;
}

.record_box .info-content p {
  color: #000 !important;
  font-size: 16px;
}

.record_box .info-content h2 {
  font-size: 20px;
}

.record_box .icon-search i {
  font-size: 20px;
}

.record_box .completed {
  color: #00BA78;
}

.record_box .unpaid {
  color: #F6A900;
}

.record_box .finishing {
  color: #00B9FF;
}

.record_box .finishing::before {
  background-color: transparent;
}

.record_box .charging {
  color: #F87D24;
}

.record_box .charging::before {
  background-color: transparent;
}

.detail_box .card-header {
  background-color: transparent;
  border: none;
}

.detail_box .card {
  background-color: #F9F9F9 !important;
  border: none !important;
  border-radius: 15px;
  width: 100%;
  height: 100%;
  padding: 10px 0px;
}

/* 内容左右对齐样式 */
.detail-row {
  display: flex;
  justify-content: space-between;
  margin-bottom: 8px;
}

.detail-label {
  color: #000;
}

.detail-value {
  text-align: right;
  font-weight: 500;
}

/* 箭头图标状态切换 - 纯CSS实现 */
.arrow-icon::before {
  content: "\f107";
  /* 默认显示向下箭头 */
  font-family: "FontAwesome";
  /* 指定Font Awesome字体 */
  transition: transform 0.3s ease-in-out;
}

.card-header:not(.collapsed) .arrow-icon::before {
  content: "\f106";
  /* 展开时显示向上箭头 */
  font-family: "FontAwesome";
  /* 指定Font Awesome字体 */
  transition: transform 0.3s ease-in-out;
}

.detail_box .amount {
  color: #FF7E47;
  font-size: 28px;
  font-weight: normal;
}

.detail_box .time {
  color: #5EDFB1;
  font-weight: normal;
  font-size: 28px;
}

.detail_box .energy {
  color: #FFB57A;
  font-weight: normal;
  font-size: 28px;
}

.detail_box .card {
  position: relative;
}

.detail_box i {
  position: absolute;
  right: 20px;
  font-size: 24px;
}

.detail_box .detail_end i {
  top: 20px;
}

.detail_box h6 {
  font-size: 18px;
}

.time_card {
  background-image: url(../img/time_bg.png);
  background-repeat: no-repeat;
  background-position: right;
  background-size: contain;
}

.energy_card {
  background-image: url(../img/flash_bg.png);
  background-repeat: no-repeat;
  background-position: right;
  background-size: contain;
}

.amount_card {
  background-image: url(../img/amount_bg.png);
  background-repeat: no-repeat;
  background-position: right;
  background-size: contain;
}

/* 卡片容器 */
.charge-card {
  background-color: #2A9B73;
  /* 绿色背景 */
  border-radius: 10px;
  padding: 15px 20px;
  color: white;
  width: 100%;
  position: relative;
  margin: 0 auto 20px;
}

.charge-card h3 {
  font-size: 1.1em;
  margin-bottom: 10px;
  font-weight: normal;
}

/* 内容布局 */
.card-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.left-section .amount {
  font-size: 18px;
  font-weight: normal;
  margin-bottom: 5px;
  color: #fff;
}

.left-section .timestamp {
  font-size: 0.9em;
  color: #fff;
  margin-bottom: 0;
}

.right-section {
  display: flex;
  align-items: center;
  gap: 5px;
}

.right-section span {
  font-size: 1em;
  padding-right: 10px;
}

.right-section .fa-chevron-right {
  font-size: 1.2em;
}

.pay_status_com {
  color: #1CA172;
  font-size: 24px;
}

.total_amount b {
  font-weight: normal;
  font-size: 16px;
}

.total_amount span {
  font-size: 20px;
  color: #FF8616;
}

.payment .card {
  padding-top: 40px;
}

.pay_status_un {
  color: #F6A900;
}

/* 底部支付栏 */
.payment-footer {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: white;
  padding: 15px 20px;
  border-top: 1px solid #e0e0e0;
  box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.05);
  display: flex;
  justify-content: space-between;
  align-items: center;
  z-index: 100;
}

/* 金额区域 */
.total-amount {
  text-align: left;
}

.total-amount span {
  display: block;
}

.total-amount .label {
  font-size: 0.9em;
  color: #666;
}

.total-amount .amount {
  font-size: 1.2em;
  font-weight: bold;
  color: #FF8616;
}

/* 支付按钮 */
.payment-button {
  background-color: #2A9B73;
  color: white;
  border: none;
  padding: 10px 20px;
  border-radius: 10px;
  font-size: 1em;
  cursor: pointer;
  transition: background-color 0.3s;
  display: flex;
  align-items: center;
}

.payment-button:hover {
  background-color: #1E7D5A;
}

.trail_content {
  margin: 30px 0px !important;
}

/* 遮罩层 */
.mask {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
  z-index: 999;
  animation: fadeIn 0.3s ease-in-out;
}

/* 弹窗容器 */
.popup {
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  z-index: 1000;
  /*            display: none;*/
  animation: slideIn 0.3s ease-out;
}

.popup-inner {
  background: #fff;
  padding: 30px;
  border-radius: 15px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  text-align: center;
  width: 280px;
  max-width: 90%;
  margin: 0 auto;
}

.popup-title {
  font-size: 18px;
  color: #2FD79B;
  margin: 15px 0;
}

.popup-btn {
  background: #2FD79B;
  color: #fff;
  border: none;
  padding: 10px 25px;
  border-radius: 5px;
  cursor: pointer;
  transition: background 0.3s;
  font-size: 1em;
  text-decoration: none;
}

body.no-scroll {
  overflow: hidden;
  position: fixed;
  width: 100%;
  top: 0;
}

/* 动画效果 */
@keyframes fadeIn {
  from {
    opacity: 0;
  }

  to {
    opacity: 1;
  }
}

@keyframes slideIn {
  from {
    transform: translate(-50%, -40%);
    opacity: 0;
  }

  to {
    transform: translate(-50%, -50%);
    opacity: 1;
  }
}

.park_status_occupied,
.park_status_vacancy,
.park_status_offline {
  vertical-align: text-bottom;
}

.park_status_occupied img {
  width: 20px;
  display: inline-block;
  filter: invert(28%) sepia(89%) saturate(5703%) hue-rotate(354deg) brightness(101%) contrast(104%);
}

.park_status_occupied b {
  display: inline-block;
  padding-left: 3px;
  font-size: 14px;
  vertical-align: middle;
  font-weight: normal;
  color: #FF3D3D;
}

.park_status_vacancy img {
  width: 20px;
  display: inline-block;
  filter: invert(40%) sepia(80%) saturate(600%) hue-rotate(120deg) brightness(90%) contrast(90%);
}

.park_status_vacancy b {
  display: inline-block;
  padding-left: 3px;
  font-size: 14px;
  vertical-align: middle;
  font-weight: normal;
  color: #00905D;
}

.park_status_offline img {
  width: 20px;
  display: inline-block;
  filter: grayscale(100%) brightness(0.5) contrast(1.5);
}

.park_status_offline b {
  display: inline-block;
  padding-left: 3px;
  font-size: 14px;
  vertical-align: middle;
  font-weight: normal;
  color: #B5B5BA;
}


/* auth box  */
.auth_content {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 60vh;
}

.auth_content .title {
  text-align: center;
  font-size: 16px;
  font-weight: 500;
  color: #333;
  margin-bottom: 1.5rem;
}

.card-input {
  display: flex;
  gap: 0.5rem;
  margin-bottom: 2rem;
  justify-content: center;
}

.input-box {
  width: 60px;
  height: 60px;
  border: 2px solid #d0d0d0;
  border-radius: 8px;
  font-size: 1.2rem;
  text-align: center;
  outline: none;
  transition: border-color 0.3s;
}

.input-box:focus {
  border-color: #00905D;
}

.input-box.error {
  border-color: #FF3D3D;
}

.submit-btn {
  display: block;
  width: 40%;
  padding: 0.8rem;
  background-color: #00905D;
  color: white;
  border: none;
  border-radius: 8px;
  font-size: 1rem;
  justify-content: center;
  cursor: pointer;
  transition: background-color 0.3s;
  margin: 0 auto;
}

.submit-btn:hover {
  background-color: #007A4F;
}

.error-message {
  color: #FF3D3D;
  font-size: 0.9rem;
  text-align: center;
  margin-top: 0.5rem;
  display: none;
}

/* bottom menu */
.bottom-menu {
  position: fixed;
  left: 0;
  bottom: -300px;
  width: 100%;
  max-height: 60vh;
  background: #fff;
  border-top-left-radius: 12px;
  border-top-right-radius: 12px;
  transition: bottom .3s;
  overflow-y: auto;
  z-index: 1000;
}

.bottom-menu.show {
  bottom: 0;
}

.bottom-menu-item {
  width: 100%;
  padding: 16px;
  border: none;
  border-bottom: 1px solid #eee;
  background: transparent;
  text-align: center;
  font-size: 1rem;
}

.bottom-menu-item:last-child {
  border-bottom: none;
  color: #dc3545;
}

.bottom-menu-item:active {
  background: #f1f1f1;
}

@media (min-width:576px) {
  .bottom-menu {
    max-width: 400px;
    left: 50%;
    transform: translateX(-50%);
  }
}

/* 选择时间/电量 */
.charge-input:focus {
  border-color: #54C49C;
  box-shadow: 0 0 0 0.25rem rgba(84, 196, 156, 0.25);
}

.loading-dots {
  font-family: monospace;
  font-size: 1.4em;
  letter-spacing: .2em;
  display: inline-flex;
}

.loading-dots span {
  opacity: 0.2;
  animation: fade 1.5s infinite;
}

.loading-dots span:nth-child(1) {
  animation-delay: 0s;
}

.loading-dots span:nth-child(2) {
  animation-delay: 0.3s;
}

.loading-dots span:nth-child(3) {
  animation-delay: 0.6s;
}

@keyframes fade {
  0% {
    opacity: 0.2;
  }

  30% {
    opacity: 1;
  }

  60% {
    opacity: 0.2;
  }

  100% {
    opacity: 0.2;
  }
}

/** Check Payment */
.payment-card {
  padding: 2rem 1.5rem;
}

/* 按钮颜色与动效 */
.btn-check-custom {
  background-color: #4CAF50;
  border-color: #4CAF50;
  font-size: 1.25rem;
  padding: .75rem 2.5rem;
  transition: background-color .3s;
  color: #fff;
}

.btn-check-custom:hover {
  background-color: #45a049;
  border-color: #45a049;
  color: #fff;
}

.btn-check-custom:active {
  transform: translateY(1px);
}