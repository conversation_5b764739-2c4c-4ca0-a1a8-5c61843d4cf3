<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <title lang="en_US">webTitleChargePlug</title>
    <link href="css/bootstrap.min.css" rel="stylesheet">
    <link href="css/font-awesome.min.css" rel="stylesheet">
    <script type="text/javascript">
        document.write('<link rel="stylesheet" href="css/style.css?time=' + new Date().getTime() + '">');
    </script>
    <script src="js/jquery-3.7.1.min.js"></script>
    <script src="js/jquery-lang.js" charset="utf-8" type="text/javascript"></script>
    <script src="js/sweetalert2.all.min.js"></script>
    <link rel="stylesheet" href="css/mescroll.min.css">
    <script src="js/mescroll.min.js" charset="utf-8"></script>
    <script src="js/common/common.js"></script>
</head>

<body>

    <!-- 顶部栏 -->
    <header class="header">
        <button class="back-button">
            <a href="site.html" class="back-icon"><i class="fa fa-angle-left"></i></a>
        </button>
        <div class="header-title"></div>
        <div class="language-button">
            <div class="dropdown">
                <button class="btn btn-lang dropdown-toggle" type="button" id="langDropdown" data-bs-toggle="dropdown" aria-expanded="false">
                    <i class="fa fa-globe" aria-hidden="true"></i>
                </button>
                <ul class="dropdown-menu dropdown-menu-end" aria-labelledby="langDropdown">
                    <li><a class="dropdown-item" href="javascript:void(0);" data-lang="en_US" onclick="changeLanguage('en_US')">English</a></li>
                    <li><a class="dropdown-item" href="javascript:void(0);" data-lang="zh_HK" onclick="changeLanguage('zh_HK')">繁體中文</a></li>
                </ul>
            </div>
        </div>
    </header>

    <div class="content">

        <div class="container">
            <div class="search-container">
                <input id="connector-search" type="search" class="search-input" onsearch="searchConnector()" placeholder="searchConnector" lang="en_US" data-lang-content="false">
                <button type="button" class="search-button" onclick="searchConnector()">
                    <i class="fa fa-search search-icon"></i>
                </button>
            </div>
        </div>

        <div id="mescroll" class="mescroll">
            <div class="mescroll-box"></div>
        </div>


    </div>

    <!-- 页面加载动画 -->
    <div id="pageLoadingOverlay">
        <div class="charging-container">
            <i class="fa fa-car car-icon"></i>
            <div class="loading-text"></div>
        </div>
    </div>

    <!-- 加载动画 -->
    <div id="loadingOverlay">
        <div class="loading-spinner"></div>
        <div class="loading-text"></div>
    </div>

</body>

</html>

<script src="js/bootstrap.bundle.min.js"></script>
<script>
    var currentSiteNumber = getLocalStorage('site'); // 场地编号
    var siteInfo = null; // 场地信息
    var current = 1; // 当前页码
    var size = 10; // 分页数据条数
    var total = 0; // 总共条数
    var pages = 0; // 总分页数量
    var mescroll = null; // 下拉上拉插件
    var zoneList = []; // 区域列表
    var otherZoneList = []; // 其他区域列表
    var lastUrl = 'site.html';

    $(document).ready(function () {
        // 初始化语言包，默认会回调pageReady函数(如存在)
        initLang();
    });

    // 页面初始化
    function pageReady() {
        removeLocalStorage('connector');
        removeLocalStorage('settingToken');
        // 如果依旧为空，则跳转至site.html
        if (isEmptyString(currentSiteNumber)) {
            window.location.href = lastUrl;
            return;
        }
        if (!isLocalStorageAvailable()) {
            lastUrl += `?site=${currentSiteNumber}`;
        }
        // 获取场地信息
        getSite(currentSiteNumber).then(() => {
            // 下拉上拉插件
            mescroll = initMeScroll(downCallback, pageConnector, upCallback, current, size);
        });

    }

    // 获取场地信息
    function getSite(siteNumber) {
        return new Promise((resolve, reject) => {
            ajaxPostRequestJsonAndCallback({
                url: "api/getSite.php",
                params: {
                    language_code: currentLanguageCode,
                    site_number: siteNumber,
                },
                successCallback: function (data) {
                    siteInfo = data.data;
                    $(".header > .header-title").text(siteInfo.name);
                    resolve(siteInfo);
                },
                errorCallback: function (message) {
                    Swal.fire({
                        title: window.lang.translate('warning'),
                        confirmButtonText: window.lang.translate('btnConfirm'),
                        confirmButtonColor: '#54C49C',
                        text: message,
                        icon: 'error',
                        allowOutsideClick: false,
                        showCancelButton: true,
                        cancelButtonText: window.lang.translate('btnGoBack') // 自定义返回按钮文本
                    }).then(function (result) {
                        if (result.isConfirmed) {
                            // 点击确认按钮，刷新当前页面
                            window.location.reload();
                        } else if (result.dismiss === Swal.DismissReason.cancel) {
                            // 点击返回按钮，跳转到上一页
                            window.location.href = lastUrl;
                        }
                    });
                    reject(message);
                }
            });
        })
    }

    // 获取充电枪列表
    function pageConnector(page = null) {
        const params = {
            language_code: currentLanguageCode,
            site_number: currentSiteNumber,
            keyword: $("#connector-search").val(),
            current: page?.num || current,
            size: page?.size || size,
        };

        ajaxPostRequestJsonAndCallback({
            url: "api/pageConnector.php",
            params: params,
            successCallback: function (data) {
                var result = data.data;
                current = result.current;
                size = result.size;
                total = result.total;
                pages = result.pages;
                if (mescroll) {
                    mescroll.endByPage(result?.records.length, pages);
                }
                // 第一页清空
                if ((page?.num || current) == 1) {
                    $('#mescroll .mescroll-box').html('');
                }
                if (isArrayAndNotEmpty(result.records)) {
                    const connectorList = result.records;
                    var currentZoneList = [];
                    var currentOtherZoneList = [];
                    // 按照zone分类
                    for (let index = 0; index < connectorList.length; index++) {
                        const connector = connectorList[index];
                        // 如果没有zone_name或者是null或空字符串
                        if (isEmptyString(connector.zone_name)) {
                            currentOtherZoneList.push(connector)
                        } else {
                            const zoneName = connector.zone_name;
                            const zone = currentZoneList.find(obj => obj.zoneName === zoneName);
                            if (zone) {
                                zone.connectorList.push(connector);
                            } else {
                                currentZoneList.push({
                                    zoneName: zoneName,
                                    zoneIndex: zoneList.length,
                                    connectorList: [connector]
                                });
                            }
                        }
                    }

                    // 首先循环有名称的zone
                    for (let index = 0; index < currentZoneList.length; index++) {
                        const zone = currentZoneList[index];
                        const zoneName = zone.zoneName;
                        const zoneIndex = zone.zoneIndex;
                        const connectorList = zone.connectorList;

                        var connectorHtml = ``;
                        for (let index = 0; index < connectorList.length; index++) {
                            const connector = connectorList[index];
                            connectorHtml += getConnectorHtml(connector);
                        }

                        // 判断zone是否在页面已存在
                        const zoneListBox = $(`#zone_list_box_${zoneIndex} .charging_list`);
                        if (zoneListBox.length > 0) {
                            zoneListBox.append(connectorHtml);
                        } else {
                            // 如果不存在，则创建
                            const zoneHtml = `<div id="zone_tag_${zoneIndex}" class="floor_tag">
                                <div class="tag">${zoneName}</div>
                            </div>
                            <div id="zone_list_box_${zoneIndex}" class="container list_box">
                                <div class="list-group d-grid gap-2 border-0 w-auto charging_list">
                                    ${connectorHtml}
                                </div>
                            </div>`;
                            // 然后追加到.mescroll-box中，但是如果存在#zone_tag_other，则追加到#zone_tag_other之前
                            const zoneListContainer = $('#mescroll .mescroll-box');
                            const otherZoneListBox = zoneListContainer.find('#zone_tag_other');
                            if (otherZoneListBox.length > 0) {
                                otherZoneListBox.before(zoneHtml);
                            } else {
                                zoneListContainer.append(zoneHtml);
                            }
                        }
                    }

                    // 然后循环currentOtherZoneList
                    var connectorHtml = '';
                    const zoneName = window.lang.translate('otherZone');
                    for (let index = 0; index < currentOtherZoneList.length; index++) {
                        const connector = currentOtherZoneList[index];
                        connectorHtml += getConnectorHtml(connector);
                    }

                    // 判断other_zone是否在页面已存在
                    const zoneListBox = $("#zone_list_box_other .charging_list");
                    if (zoneListBox.length > 0) {
                        zoneListBox.append(connectorHtml);
                    } else {
                        // 如果不存在，则创建
                        const zoneHtml = `<div id="zone_tag_other" class="floor_tag">
                            <div class="tag">${zoneName}</div>
                        </div>
                        <div id="zone_list_box_other" class="container list_box">
                            <div class="list-group d-grid gap-2 border-0 w-auto charging_list">
                                ${connectorHtml}
                            </div>
                        </div>`;
                        const zoneListContainer = $('#mescroll .mescroll-box');
                        zoneListContainer.append(zoneHtml);
                    }

                    // 合并到zoneList和otherZoneList
                    // 创建一个以 zoneName 为 key 的 Map，方便查找
                    const zoneMap = new Map(zoneList.map(item => [item.zoneName, item]));
                    for (const currentZone of currentZoneList) {
                        const existing = zoneMap.get(currentZone.zoneName);
                        if (existing) {
                            // 如果 zoneName 已存在，合并 connectorList
                            existing.connectorList.push(...currentZone.connectorList);
                        } else {
                            // 否则，追加整条数据
                            zoneList.push({ ...currentZone });
                            zoneMap.set(currentZone.zoneName, currentZone); // 更新 Map
                        }
                    }
                    otherZoneList.push(...currentOtherZoneList);


                    // 同时存在多个zone时，显示other_zone的tag，否则隐藏
                    if (zoneList.length > 0 && otherZoneList.length > 0) {
                        $("#zone_tag_other").show();
                    } else {
                        $("#zone_tag_other").hide();
                    }

                    if (!page && mescroll) {
                        mescroll.endSuccess();
                    }

                }
            },
            beforeErrorCallback: function () {
                if (mescroll) {
                    mescroll.endErr();
                }
            },
            errorCallback: function (message) {
                Swal.fire({
                    title: message || window.lang.translate('warning'),
                    text: window.lang.translate('requestFailed'),
                    icon: 'warning',
                    allowOutsideClick: false,
                    showCancelButton: true,
                    confirmButtonText: window.lang.translate('btnRetry'),
                    confirmButtonColor: '#54C49C',
                    cancelButtonText: window.lang.translate('cancel') // 自定义返回按钮文本
                }).then(function (result) {
                    if (result.isConfirmed) {
                        mescroll.resetUpScroll();
                    } else {
                        // 刷新页面
                        window.location.reload();
                    }
                });
            }
        });
    }

    // 搜索
    function searchConnector() {
        zoneList = [];
        otherZoneList = [];
        mescroll.resetUpScroll();
    }

    // 下拉刷新
    function downCallback() {
        zoneList = [];
        otherZoneList = [];
        mescroll.resetUpScroll();
    }

    // 上拉加载
    function upCallback(page) {
        pageConnector(page);
    }

    // 获取传感器状态信息
    function getInfoByParkSensorStatus(parkSensorStatus) {
        var parkSensorStatusText = '';
        var parkSensorClass = '';
        switch (parkSensorStatus) {
            case 'OCCUPIED':
                parkSensorClass = 'park_status_occupied';
                parkSensorStatusText = window.lang.translate('parkSensorStatusOCCUPIED');
                break;
            case 'VACANCY':
                parkSensorClass = 'park_status_vacancy';
                parkSensorStatusText = window.lang.translate('parkSensorStatusVACANCY');
                break;
            case 'OFFLINE':
                parkSensorClass = 'park_status_offline';
                parkSensorStatusText = window.lang.translate('parkSensorStatusOFFLINE');
                break;
        }
        return {
            parkSensorClass: parkSensorClass,
            parkSensorStatusText: parkSensorStatusText,
        }
    }

    // 获取充电枪状态及样式、点击事件
    function getInfoByConnectorStatus(connectorStatus, connectorNumber = 0) {
        var connectorStatusText = '';
        var connectorStatusClass = '';
        var clickEvent = '';
        switch (connectorStatus) {
            case 'OFFLINE':
                connectorStatusText = window.lang.translate('connectorStatusOFFLINE');
                connectorStatusClass = 'offline';
                break;
            case 'AVAILABLE':
                connectorStatusText = window.lang.translate('connectorStatusAVAILABLE');
                connectorStatusClass = 'available';
                break;
            case 'PREPARING':
                connectorStatusText = window.lang.translate('connectorStatusPREPARING');
                connectorStatusClass = 'preparing';
                clickEvent = `goPreparingPage('${connectorNumber}')`;
                break;
            case 'CHARGING':
                connectorStatusText = window.lang.translate('connectorStatusCHARGING');
                connectorStatusClass = 'charging';
                clickEvent = `goChargingPage('${connectorNumber}')`;
                break;
            case 'FINISHING':
                connectorStatusText = window.lang.translate('connectorStatusFINISHING');
                connectorStatusClass = 'finishing';
                clickEvent = `goFinishingAuthPage('${connectorNumber}')`;
                break;
            case 'UNAVAILABLE':
                connectorStatusText = window.lang.translate('connectorStatusUNAVAILABLE');
                connectorStatusClass = 'unavailable';
                break;
            case 'QUEUING':
                connectorStatusText = window.lang.translate('connectorStatusQUEUING');
                connectorStatusClass = 'queuing';
                break;
            case 'FAULTED':
                connectorStatusText = window.lang.translate('connectorStatusFAULTED');
                connectorStatusClass = 'faulted';
        }
        return {
            connectorStatusClass: connectorStatusClass,
            connectorStatusText: connectorStatusText,
            clickEvent: clickEvent,
        }
    }

    // 获取充电枪类型的文字和图片
    function getInfoByConnectorType(connectorType) {
        var connectorTypeText = '';
        var connectorTypeImgUrl = '';
        switch (connectorType) {
            case 'TYPE_1':
                connectorTypeText = window.lang.translate('connectorTypeTYPE_1');
                connectorTypeImgUrl = 'img/type1.png';
                break;
            case 'TYPE_2':
                connectorTypeText = window.lang.translate('connectorTypeTYPE_2');
                connectorTypeImgUrl = 'img/type2.png';
                break;
            case 'AC_GBT':
                connectorTypeText = window.lang.translate('connectorTypeAC_GBT');
                connectorTypeImgUrl = 'img/gbt_ac.png';
                break;
            case 'CCS_1':
                connectorTypeText = window.lang.translate('connectorTypeCCS_1');
                connectorTypeImgUrl = 'img/ccs1.png';
                break;
            case 'CCS_2':
                connectorTypeText = window.lang.translate('connectorTypeCCS_2');
                connectorTypeImgUrl = 'img/ccs2.png';
                break;
            case 'CHADEMO':
                connectorTypeText = window.lang.translate('connectorTypeCHADEMO');
                connectorTypeImgUrl = 'img/chademo.png';
                break;
            case 'DC_GBT':
                connectorTypeText = window.lang.translate('connectorTypeDC_GBT');
                connectorTypeImgUrl = 'img/gbt_dc.png';
                break;
        }
        return {
            connectorTypeText: connectorTypeText,
            connectorTypeImgUrl: connectorTypeImgUrl,
        }
    }

    // 组装充电枪HTML
    function getConnectorHtml(connector) {
        var parkSensorInfo = getInfoByParkSensorStatus(connector.park_sensor_status);
        var parkSensorClass = parkSensorInfo.parkSensorClass;
        var parkSensorStatus = parkSensorInfo.parkSensorStatusText;

        var connectorStatusInfo = getInfoByConnectorStatus(connector.status, connector.connector_number);
        var connectorStatusClass = connectorStatusInfo.connectorStatusClass;
        var connectorStatus = connectorStatusInfo.connectorStatusText;
        var clickEvent = connectorStatusInfo.clickEvent;

        var connectorTypeInfo = getInfoByConnectorType(connector.connector_type);
        var connectorTypeText = connectorTypeInfo.connectorTypeText;
        var connectorTypeImgUrl = connectorTypeInfo.connectorTypeImgUrl;

        const connectorHtml = `<div class="position-relative charging_card ${connectorStatusClass}" onclick="${clickEvent}">
            <div class="row">
                <div class="col-7">
                    <label class="list-group-item py-3">
                        <strong class="fw-semibold">${connector.name}</strong>
                        <span class="${parkSensorClass}" ${!parkSensorStatus ? `style="display: none"` : ''}>
                            <img src="img/car.svg"><b>${parkSensorStatus}</b>
                        </span>
                        <span class="d-block status_${connectorStatusClass}" ${!connectorStatusClass ? `style="display: none"` : ''} >
                            ${connectorStatus}
                        </span>
                    </label>
                </div>

                <div class="col-5">
                    <div class="py-3">
                        <div class="brand">
                            <img src="img/brand.png" class="img-responsive">
                        </div>

                        <div class="plug_type" ${(!connectorTypeImgUrl && !connectorTypeText) ? `style="display: none"` : ''}>
                            <img src="${connectorTypeImgUrl}" class="img-responsive" ${!connectorTypeImgUrl ? `style="display: none"` : ''}>
                            <span class="">${connectorTypeText}</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        `;

        return connectorHtml;
    }

    // 跳转开始充电页面
    function goPreparingPage(connectorNumber) {
        // 先从otherZone获取当前充电枪的setting_token
        var currentConnector = otherZoneList.find(item => item.connector_number === connectorNumber);
        if (!currentConnector) {
            // 再从zone获取
            currentConnector = zoneList
                .flatMap(z => z.connectorList) // 把所有 connector 摊平到一层
                .find(c => c.connector_number === connectorNumber);
        }
        if (!currentConnector) {
            window.location.reload();
            return;
        }
        verifyConnectorStartCharge(connectorNumber, currentConnector.setting_token).then(() => {
            goNextPage('charge_confirm.html', connectorNumber, currentConnector.setting_token);
        });
    }

    // 跳转充电页面
    function goChargingPage(connectorNumber) {
        goNextPage('charging.html', connectorNumber);
    }

    // 跳转充电结束页面
    function goFinishingAuthPage(connectorNumber) {
        goNextPage('finishing_auth.html', connectorNumber);
    }

    // 跳转页面公共方法
    function goNextPage(pageName, connectorNumber, settingToken = '') {
        if (isLocalStorageAvailable()) {
            setLocalStorage('connector', connectorNumber);
            if (!isEmptyString(settingToken)) {
                setLocalStorage('settingToken', settingToken);
            }
            window.location.href = pageName;
        } else {
            var url = `${pageName}?site=${siteInfo.siteNumber}&connector=${connectorNumber}`;
            if (!isEmptyString(settingToken)) {
                url += `&settingToken=${settingToken}`;
            }
            window.location.href = url;
        }
    }

    // 验证充电枪setting_token
    function verifyConnectorStartCharge(connectorNumber, settingToken) {
        return new Promise((resolve, reject) => {
            ajaxPostRequestJsonAndCallback({
                url: "api/verifyConnectorStartCharge.php",
                params: {
                    language_code: currentLanguageCode,
                    connector_number: connectorNumber,
                    setting_token: settingToken,
                },
                beforeSendCallback: function () {
                    showPageLoading();
                },
                successCallback: function (data) {
                    if (data.data === true) {
                        resolve(data);
                    } else {
                        Swal.fire({
                            title: data?.message || window.lang.translate('warning'),
                            text: window.lang.translate('tryAgain'),
                            icon: 'warning',
                            showConfirmButton: false,
                            timer: 3000,
                            timerProgressBar: true,
                            willClose: function () {
                                // 刷新当前页面
                                window.location.reload();
                            }
                        });
                        reject(data);
                    }
                },
                errorCallback: function (message) {
                    Swal.fire({
                        title: message || window.lang.translate('warning'),
                        text: window.lang.translate('tryAgain'),
                        icon: 'warning',
                        showConfirmButton: false,
                        timer: 3000,
                        timerProgressBar: true,
                        willClose: function () {
                            // 刷新当前页面
                            window.location.reload();
                        }
                    });
                    reject(message);
                },
                completeCallback: function () {
                    hidePageLoading();
                }
            });
        })
    }
</script>
