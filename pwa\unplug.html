<!DOCTYPE html>
<html lang="en">

<head>
	<meta charset="utf-8">
	<meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
	<title></title>
	<link href="css/bootstrap.min.css" rel="stylesheet">
	<link href="css/font-awesome.min.css" rel="stylesheet">
	<link href="css/bootstrap-slider.min.css" rel="stylesheet">
	<script type="text/javascript">
		document.write('<link rel="stylesheet" href="css/style.css?time=' + new Date().getTime() + '">');
	</script>
	<script src="js/jquery-3.7.1.min.js"></script>
	<script src="js/jquery-lang.js" charset="utf-8" type="text/javascript"></script>
	<script src="js/sweetalert2.all.min.js"></script>
	<script src="js/common/common.js"></script>
</head>

<body>
	<!-- 顶部栏 -->
	<header class="header">
		<div class="header-title"><i class="fa fa-bolt" aria-hidden="true"></i> </div>
	</header>


	<div class="content trail_content">
		<div class="status text-center">
			<img src="img/red-ball.gif" class="img-responsive center-block">
		</div>
	</div>

	<div class="container text-center">
		<!-- 文字动画容器 -->
		<div class="animate-text mb-8 text-center" lang="en_US">unplugStopChargingHtml</div>

		<p class="subtext" lang="en_US">unplugBottomText</p>
	</div>

	<!-- 页面加载动画 -->
	<div id="pageLoadingOverlay">
		<div class="charging-container">
			<i class="fa fa-car car-icon"></i>
			<div class="loading-text"></div>
		</div>
	</div>

	<!-- 加载动画 -->
	<div id="loadingOverlay">
		<div class="loading-spinner"></div>
		<div class="loading-text"></div>
	</div>


</body>
<script>
	var currentSiteNumber = getLocalStorage('site'); // 场地编号
	var currentConnectorNumber = getLocalStorage('connector'); // 充电枪编号
	var uniqueId = getLocalStorage(`uniqueId-${currentConnectorNumber}`);
	var currentChargeRecordNumber = getLocalStorage(`chargeRecordNumber-${currentConnectorNumber}`);
	var connectorInfo = null; // 充电枪信息
	var lastUrl = 'charging.html';
	var nextUrl = 'site.html';
	const timeout = 1 * 60 * 1000; // 1分钟 轮询的超时时间
	const interval = 2 * 1000; // 2秒 轮询的轮询间隔

	$(document).ready(function () {
		// 初始化语言包，默认会回调pageReady函数(如存在)
		initLang();
	});

	// 页面初始化
	function pageReady() {
		// 如果为空，则跳转至charging.html
		if (isEmptyString(currentSiteNumber) || isEmptyString(currentConnectorNumber) || isEmptyString(uniqueId) || isEmptyString(currentChargeRecordNumber)) {
			window.location.href = `charging.html?site=${currentSiteNumber || getQueryVariable('site', '')}&connector=${currentConnectorNumber || getQueryVariable('connector', '')}&uniqueId-${currentConnectorNumber}=${uniqueId || getQueryVariable(`uniqueId-${currentConnectorNumber}`, '')}`;
			return;
		}
		if (!isLocalStorageAvailable()) {
			lastUrl += `?site=${currentSiteNumber}&connector=${currentConnectorNumber}&uniqueId-${currentConnectorNumber}=${uniqueId}`;
			nextUrl += `?connector=${currentConnectorNumber}&uniqueId-${currentConnectorNumber}=${uniqueId}&chargeRecordNumber-${currentConnectorNumber}=${currentChargeRecordNumber}`;
		}
		// 获取充电枪信息
		getConnector(currentConnectorNumber).then(() => {
			setTimeout(() => {
				remoteStop(uniqueId, currentConnectorNumber);
			}, 500);
		});
	}

	// 获取充电枪信息
	function getConnector(connectorNumber) {
		return new Promise((resolve, reject) => {
			ajaxPostRequestJsonAndCallback({
				url: "api/getConnector.php",
				params: {
					language_code: currentLanguageCode,
					connector_number: connectorNumber,
				},
				successCallback: function (data) {
					connectorInfo = data.data;
					$("title").text(connectorInfo.name);
					$(".header > .header-title").append(connectorInfo.name);
					resolve(connectorInfo);
				},
				errorCallback: function (message) {
					Swal.fire({
						title: window.lang.translate('warning'),
						confirmButtonText: window.lang.translate('btnConfirm'),
						confirmButtonColor: '#54C49C',
						text: message,
						icon: 'error',
						allowOutsideClick: false,
						showCancelButton: true,
						cancelButtonText: window.lang.translate('btnGoBack') // 自定义返回按钮文本
					}).then(function (result) {
						if (result.isConfirmed) {
							// 点击确认按钮，刷新当前页面
							window.location.reload();
						} else if (result.dismiss === Swal.DismissReason.cancel) {
							// 点击返回按钮，跳转到上一页
							window.location.href = lastUrl;
						}
					});
					reject(message);
				}
			});
		})
	}

	// 远程停止充电并轮询结果
	function remoteStop(uniqueId, currentConnectorNumber) {
		let timeoutId;
		let expired = false;

		// 超时逻辑
		timeoutId = setTimeout(() => {
			expired = true;
			Swal.fire({
                title: window.lang.translate('warning'),
                text: window.lang.translate('timeout'),
                icon: 'warning',
                showConfirmButton: false,
                timer: 2000,
                timerProgressBar: true,
                willClose: function () {
                    // 跳回充电页
                    window.location.href = lastUrl;
                }
            });
		}, timeout);

		// 开始轮询
		function pollStop() {
			if (expired) return; // 超时了就别继续了

			getCallChargePointRecordStop(uniqueId).then(() => {
				if (expired) return;
				clearTimeout(timeoutId);
				// 停止充电成功，跳转至site.html页面
				if (isLocalStorageAvailable()) {
					setLocalStorage(`isStopChargingSuccess-${currentConnectorNumber}`, '1');
					window.location.href = nextUrl;
					return;
				}
				nextUrl += `&isStopChargingSuccess-${currentConnectorNumber}=1`;
				window.location.href = nextUrl;
			}).catch(() => {
				setTimeout(() => {
					pollStop();
				}, interval);
			});
		}

		remoteStopCharge(uniqueId, currentConnectorNumber).then(() => {
			pollStop();
		});
	}

	// 远程停止充电
	function remoteStopCharge(uniqueId, connectorNumber) {
		return new Promise((resolve, reject) => {
			ajaxPostRequestJsonAndCallback({
				url: "api/remoteStopCharge.php",
				params: {
					language_code: currentLanguageCode,
					unique_id: uniqueId,
					connector_number: connectorNumber,
				},
				successCallback: function (data) {
					if (data.data === true) {
						resolve(data);
					} else {
						Swal.fire({
							title: data?.message || window.lang.translate('error'),
							text: window.lang.translate('stopChargeFailed'),
							icon: 'error',
							showConfirmButton: false,
							timer: 3000,
							timerProgressBar: true,
							willClose: function () {
								// 跳回充电枪列表页
								window.location.href = lastUrl;
							}
						});
						reject(data);
					}
				},
				errorCallback: function (message) {
					Swal.fire({
						title: message || window.lang.translate('error'),
						text: window.lang.translate('stopChargeFailed'),
						icon: 'error',
						showConfirmButton: false,
						timer: 3000,
						timerProgressBar: true,
						willClose: function () {
							// 跳回充电枪列表页
							window.location.href = lastUrl;
						}
					});
					reject(message);
				}
			});
		})
	}

	// 获取调用充电记录停止充电
	function getCallChargePointRecordStop(uniqueId) {
		return new Promise((resolve, reject) => {
			ajaxPostRequestJsonAndCallback({
				url: "api/getCallChargePointRecordStop.php",
				params: {
					language_code: currentLanguageCode,
					unique_id: uniqueId,
				},
				successCallback: function (data) {
					if (data.data?.call_verify_result === true) {
						resolve(data);
					} else {
						reject(data);
					}
				},
				errorCallback: function (message) {
					reject(message);
				}
			});
		})
	}
</script>

</html>
