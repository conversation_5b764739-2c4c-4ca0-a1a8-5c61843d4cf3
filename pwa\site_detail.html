<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="utf-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
  <title>Site Detail</title>
  <link href="css/bootstrap.min.css" rel="stylesheet">
  <link href="css/font-awesome.min.css" rel="stylesheet">
  <link href="css/bootstrap-slider.min.css" rel="stylesheet">
  <script type="text/javascript">
    document.write('<link rel="stylesheet" href="css/style.css?time=' + new Date().getTime() + '">');
  </script>
  <script src="js/jquery-3.7.1.min.js"></script>
  <script src="js/jquery-lang.js" charset="utf-8" type="text/javascript"></script>
  <script src="js/sweetalert2.all.min.js"></script>
  <script src="js/common/common.js"></script>
</head>

<body>

  <div class="container-fluid">
    <div class="parking-img">
      <a href="site.html" class="site_detail_back"><i class="fa fa-angle-left" aria-hidden="true"></i></a>
      <img src="img/parking_bg.jpg">
    </div>

    <div class="position-relative site_detail_content">
      <div class="bg-white p-4 rounded-3 mt-n5">
        <div class="d-flex justify-content-between align-items-center mb-3">
          <div class="title">
            <h5>Aberdeen Car Park</h5>
            <p class="mb-0">香港仔停車場</p>
          </div>
          <div class="navigation-button text-center" style="display: none;" onclick="openMapMenu()">
            <div class="diamond"><i class="fa fa-location-arrow" aria-hidden="true"></i>
            </div>
            <p class="distance" style="display: none;"></p>
          </div>
        </div>
        <ul class="nav nav-tabs justify-content-center">
          <li class="nav-item">
            <a class="nav-link active" data-bs-toggle="tab" href="#overview">Overview</a>
          </li>
          <li class="nav-item">
            <a class="nav-link" data-bs-toggle="tab" href="#photoGallery">Photo Gallery</a>
          </li>
        </ul>
        <div class="tab-content">
          <div class="tab-pane active" id="overview">
            <div class="mb-3 border-bottom">
              <h6 class="text-muted">Info</h6>
              <p class="mb-1"><i class="fa fa-map-marker" aria-hidden="true"></i>Aberdeen, Hong Kong</p>
              <p class="mb-1"><i class="fa fa-phone" aria-hidden="true"></i>2880 1555</p>
              <p class="mb-1"><i class="fa fa-clock-o" aria-hidden="true"></i>Monday to Friday(7:45am to 8:00pm)<br>Saturday, Sunday, and public holidays (7:45am to 6:00pm)</p>
            </div>
            <div class="mb-3 border-bottom">
              <h6 class="text-muted">About</h6>
              <p>Lorem ipsum dolor sit amet consectetur. Elementum netus viverra aenean uma integer libero magna uma condimentum</p>
            </div>
            <div class="mb-3 border-bottom">
              <h6 class="text-muted">Total Charger Slot</h6>
              <p><span>1 Available</span> / 8</p>
            </div>
            <div class="mb-3">
              <h6 class="text-muted">Port Type</h6>
              <p><img src="img/ccs1.png" class="img-responsive">CCS 1</p>
            </div>
          </div>
          <div class="tab-pane" id="photoGallery">
            <div class="row g-2">
              <div class="col-6">
                <img src="img/1.jpg" alt="Photo" class="w-100 rounded-3">
              </div>
              <div class="col-6">
                <img src="img/2.jpg" alt="Photo" class="w-100 rounded-3">
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- 遮罩层 -->
  <div class="mask hide" id="mask"></div>

  <!-- 页面加载动画 -->
  <div id="pageLoadingOverlay">
    <div class="charging-container">
      <i class="fa fa-car car-icon"></i>
      <div class="loading-text"></div>
    </div>
  </div>

  <!-- 加载动画 -->
  <div id="loadingOverlay">
    <div class="loading-spinner"></div>
    <div class="loading-text"></div>
  </div>

  <!-- 底部菜单 -->
  <div class="bottom-menu" id="map-menu">
    <button class="bottom-menu-item hide" id="google-map-btn" onclick="openMap(this)" lang="en_US">googleMap</button>
    <button class="bottom-menu-item hide" id="apple-map-btn" onclick="openMap(this)" lang="en_US">appleMap</button>
    <button class="bottom-menu-item hide" id="amap-btn" onclick="openMap(this)" lang="en_US">amap</button>
    <button class="bottom-menu-item" id="map-menu-cancel-btn" onclick="closeMapMenu()" lang="en_US">btnCancel</button>
  </div>

</body>

<script src="js/bootstrap.bundle.min.js"></script>
<script>
  var currentSiteNumber = getLocalStorage('site'); // 场地编号
  var siteInfo = null; // 场地信息
  var currentLongitude = getLocalStorage('currentLongitude'); // 经度
  var currentLatitude = getLocalStorage('currentLatitude'); // 纬度
  var isSortDistance = getLocalStorage('isSortDistance', '0') === '1' ? true : false;// 是否按照距离排序
  var lastUrl = 'site.html';

  $(document).ready(function () {
    // 初始化语言包，默认会回调pageReady函数(如存在)
    initLang();
  });

  // 页面初始化
  function pageReady() {
    // 如果依旧为空，则跳转至site.html
    if (isEmptyString(currentSiteNumber)) {
      window.location.href = lastUrl;
      return;
    }
    // 获取场地信息
    getSite(currentSiteNumber).then(() => {
      // 谷歌地图
      if (!isEmptyString(siteInfo.google_map_url)) {
        $('#google-map-btn').removeClass('hide').data('map-url', siteInfo.google_map_url);
        $('.navigation-button').show();
      }
      // 苹果地图
      if (!isEmptyString(siteInfo.apple_map_url)) {
        $('#apple-map-btn').removeClass('hide').data('map-url', siteInfo.apple_map_url);
        $('.navigation-button').show();
      }
      // 高德地图
      if (!isEmptyString(siteInfo.amap_map_url)) {
        $('#amap-btn').removeClass('hide').data('map-url', siteInfo.amap_map_url);
        $('.navigation-button').show();
      }

      // 如果场地有经纬度，且场地列表页有授予位置权限，则显示距离
      if (isSortDistance && !isEmptyString(siteInfo.longitude) && !isEmptyString(siteInfo.latitude)) {
        getLocation().then(({ longitude, latitude }) => {
          currentLongitude = longitude;
          currentLatitude = latitude;
          setLocalStorage('currentLongitude', currentLongitude);
          setLocalStorage('currentLatitude', currentLatitude);
        }).catch((error) => {
          console.error("Error occurred while obtaining location:", error);
        }).finally(() => {
          if (!isEmptyString(currentLongitude) && !isEmptyString(currentLatitude)) {
            const distance = getSiteDistance(siteInfo.longitude, siteInfo.latitude);
            if (distance) {
              $('.distance').text(distance).show();
            }
          }
        });
      }
    });
  }

  // 获取场地信息
  function getSite(siteNumber) {
    return new Promise((resolve, reject) => {
      ajaxPostRequestJsonAndCallback({
        url: "api/getSite.php",
        params: {
          language_code: currentLanguageCode,
          site_number: siteNumber,
        },
        successCallback: function (data) {
          siteInfo = data.data;
          resolve(siteInfo);
        },
        errorCallback: function (message) {
          Swal.fire({
            title: window.lang.translate('warning'),
            confirmButtonText: window.lang.translate('btnConfirm'),
            confirmButtonColor: '#54C49C',
            text: message,
            icon: 'error',
            allowOutsideClick: false,
            showCancelButton: true,
            cancelButtonText: window.lang.translate('btnGoBack') // 自定义返回按钮文本
          }).then(function (result) {
            if (result.isConfirmed) {
              // 点击确认按钮，刷新当前页面
              window.location.reload();
            } else if (result.dismiss === Swal.DismissReason.cancel) {
              // 点击返回按钮，跳转到上一页
              window.location.href = lastUrl;
            }
          });
          reject(message);
        }
      });
    })
  }

  // 打开地图菜单
  function openMapMenu() {
    $('#mask').removeClass('hide');
    // 遮罩层添加一次性关闭事件
    $('#mask').one('click', function () {
      closeMapMenu();
    });

    openBottomMenu('#map-menu');
  }

  // 关闭地图菜单
  function closeMapMenu() {
    $('#google-map-btn, #apple-map-btn, #amap-btn').addClass('hide');
    $('#mask').addClass('hide');
    closeBottomMenu('#map-menu');
  }

  // 打开地图
  function openMap(that) {
    const mapUrl = $(that).data('map-url');
    if (isEmptyString(mapUrl)) {
      return;
    }
    window.open(mapUrl, '_blank');
    closeMapMenu();
  }

  // 获取site和当前位置的距离
  function getSiteDistance(longitude, latitude) {
    // 未开启距离排序，无法获取当前位置
    if (!isSortDistance || !longitude || !latitude) {
      return false;
    }

    // 地球半径（米）
    const R = 6371000;
    // 将经纬度转换为弧度
    const lat1Rad = latitude * Math.PI / 180;
    const lon1Rad = longitude * Math.PI / 180;
    const lat2Rad = currentLatitude * Math.PI / 180;
    const lon2Rad = currentLongitude * Math.PI / 180;

    // 计算经纬度的差值
    const deltaLat = lat2Rad - lat1Rad;
    const deltaLon = lon2Rad - lon1Rad;

    // 使用球面三角公式计算距离
    const a = Math.sin(deltaLat / 2) * Math.sin(deltaLat / 2) +
      Math.cos(lat1Rad) * Math.cos(lat2Rad) *
      Math.sin(deltaLon / 2) * Math.sin(deltaLon / 2);
    const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
    const distance = R * c;

    if (distance >= 1000) {
      // 转换为公里并保留一位小数
      return (distance / 1000).toFixed(1) + 'km';
    } else {
      // 小于 1000 米直接显示米数
      return Math.round(distance) + 'm';
    }
  }


</script>

</html>
