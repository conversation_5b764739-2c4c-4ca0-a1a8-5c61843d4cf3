<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <title></title>
    <link href="css/bootstrap.min.css" rel="stylesheet">
    <link href="css/font-awesome.min.css" rel="stylesheet">
    <script type="text/javascript">
        document.write('<link rel="stylesheet" href="css/style.css?time=' + new Date().getTime() + '">');
    </script>
    <script src="js/jquery-3.7.1.min.js"></script>
    <script src="js/jquery-lang.js" charset="utf-8" type="text/javascript"></script>
    <script src="js/sweetalert2.all.min.js"></script>
    <script src="js/common/common.js"></script>
</head>

<body>

    <!-- 顶部栏 -->
    <header class="header">
        <button class="back-button">
            <a href="javascript:void(0)" onclick="history.back()" class="back-icon">×</a>
        </button>
        <div class="header-title"><i class="fa fa-bolt" aria-hidden="true"></i> </div>
    </header>

    <div class="content auth_content">

        <div class="container">
            <div class="title" lang="en_US">authTitle</div>
            <div class="card-input">
                <input type="tel" class="input-box" id="code1" maxlength="1" autocomplete="one-time-code" inputmode="numeric" autofocus>
                <input type="tel" class="input-box" id="code2" maxlength="1" autocomplete="one-time-code" inputmode="numeric">
                <input type="tel" class="input-box" id="code3" maxlength="1" autocomplete="one-time-code" inputmode="numeric">
                <input type="tel" class="input-box" id="code4" maxlength="1" autocomplete="one-time-code" inputmode="numeric">
            </div>
            <button class="submit-btn" onclick="validateCode()" lang="en_US">btnConfirm</button>
            <div class="error-message" id="errorMsg" lang="en_US">authError</div>
        </div>

    </div>

    <!-- 页面加载动画 -->
    <div id="pageLoadingOverlay">
        <div class="charging-container">
            <i class="fa fa-car car-icon"></i>
            <div class="loading-text"></div>
        </div>
    </div>

    <!-- 加载动画 -->
    <div id="loadingOverlay">
        <div class="loading-spinner"></div>
        <div class="loading-text"></div>
    </div>

</body>
<script>
    var currentSiteNumber = getLocalStorage('site'); // 场地编号
    var currentConnectorNumber = getLocalStorage('connector'); // 充电枪编号
    var connectorInfo = null; // 充电枪信息
    const CORRECT_CODE = "1234"; // 正确的卡号尾号（可修改为实际号码）
    var lastUrl = 'choose_plug.html';
    var nextUrl = 'preparing.html';

    $(document).ready(function () {
        // 初始化语言包，默认会回调pageReady函数(如存在)
        initLang();
    });

    // 页面初始化
    function pageReady() {
        // 如果为空，则跳转至choose_plug.html
        if (isEmptyString(currentSiteNumber) || isEmptyString(currentConnectorNumber)) {
            window.location.href = `choose_plug.html?site=${currentSiteNumber || getQueryVariable('site', '')}`;
            return;
        }
        if (!isLocalStorageAvailable()) {
            lastUrl += `?site=${currentSiteNumber}&connector=${currentConnectorNumber}`;
            nextUrl += `?site=${currentSiteNumber}&connector=${currentConnectorNumber}`;
        }
        // 获取充电枪信息
        getConnector(currentConnectorNumber);

        const $inputs = $('.input-box');

        // 统一处理输入：只允许 0-9
        $inputs.on('input', function (e) {
            const val = e.target.value;
            // 非法字符直接清空
            if (!/^\d?$/.test(val)) {
                $(this).val('');
                return;
            }

            // 如果输入了一位，自动跳到下一个
            if (val) {
                const next = $(this).next('.input-box')[0];
                next && next.focus();
            }
        });

        // 处理删除：Backspace 或选中后按 Delete
        $inputs.on('keydown', function (e) {
            const key = e.which;
            const $this = $(this);

            // Backspace 且当前框为空，回到上一个
            if (key === 8 && !$this.val()) {
                const prev = $this.prev('.input-box')[0];
                prev && prev.focus();
                return;
            }

            // Delete 且当前框为空，跳到下一个
            if (key === 46 && !$this.val()) {
                const next = $this.next('.input-box')[0];
                next && next.focus();
            }
        });

        // 支持一次性粘贴 4 位验证码
        $inputs.on('paste', function (e) {
            e.preventDefault();
            const pasteData = (e.originalEvent.clipboardData || window.clipboardData).getData('text');
            const digits = pasteData.replace(/\D/g, '').slice(0, 4).split('');

            $inputs.each(function (idx) {
                $(this).val(digits[idx] || '');
            });
            // 粘贴完光标落在最后一个非空框
            const lastIndex = Math.min(digits.length - 1, 3);
            $inputs.eq(lastIndex).focus();
        });

        // 选中全部文字时方便直接输入替换
        $inputs.on('focus', function () {
            $(this).select();
        });
    }

    // 获取充电枪信息
    function getConnector(connectorNumber) {
        return new Promise((resolve, reject) => {
            ajaxPostRequestJsonAndCallback({
                url: "api/getConnector.php",
                params: {
                    language_code: currentLanguageCode,
                    connector_number: connectorNumber,
                },
                successCallback: function (data) {
                    connectorInfo = data.data;
                    $("title").text(connectorInfo.name);
                    $(".header > .header-title").append(connectorInfo.name);
                    resolve(connectorInfo);
                },
                errorCallback: function (message) {
                    Swal.fire({
                        title: window.lang.translate('warning'),
                        confirmButtonText: window.lang.translate('btnConfirm'),
                        confirmButtonColor: '#54C49C',
                        text: message,
                        icon: 'error',
                        allowOutsideClick: false,
                        showCancelButton: true,
                        cancelButtonText: window.lang.translate('btnGoBack') // 自定义返回按钮文本
                    }).then(function (result) {
                        if (result.isConfirmed) {
                            // 点击确认按钮，刷新当前页面
                            window.location.reload();
                        } else if (result.dismiss === Swal.DismissReason.cancel) {
                            // 点击返回按钮，跳转到上一页
                            window.location.href = lastUrl;
                        }
                    });
                    reject(message);
                }
            });
        })
    }

    // 验证函数
    function validateCode() {
        const code1 = document.getElementById('code1').value;
        const code2 = document.getElementById('code2').value;
        const code3 = document.getElementById('code3').value;
        const code4 = document.getElementById('code4').value;
        const enteredCode = code1 + code2 + code3 + code4;

        const errorMsg = document.getElementById('errorMsg');
        const inputs = document.querySelectorAll('.input-box');

        if (enteredCode.length !== 4) {
            errorMsg.style.display = 'block';
            inputs.forEach(input => input.classList.add('error'));
            return;
        }

        if (enteredCode === CORRECT_CODE) {
            // 验证通过，跳转到下一页（可替换为实际URL）
            window.location.href = nextUrl;
        } else {
            errorMsg.style.display = 'block';
            inputs.forEach(input => input.classList.add('error'));
            // 清空输入框并移除错误样式（可选）
            setTimeout(() => {
                inputs.forEach(input => {
                    input.value = '';
                    input.classList.remove('error');
                });
                errorMsg.style.display = 'none';
                document.getElementById('code1').focus();
            }, 3000);
        }
    }
</script>

</html>
