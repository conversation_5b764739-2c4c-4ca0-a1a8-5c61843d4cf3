<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <title></title>
    <link href="css/bootstrap.min.css" rel="stylesheet">
    <link href="css/font-awesome.min.css" rel="stylesheet">
    <link href="css/bootstrap-slider.min.css" rel="stylesheet">
    <script type="text/javascript">
        document.write('<link rel="stylesheet" href="css/style.css?time=' + new Date().getTime() + '">');
    </script>
    <script src="js/jquery-3.7.1.min.js"></script>
    <script src="js/jquery-lang.js" charset="utf-8" type="text/javascript"></script>
    <script src="js/sweetalert2.all.min.js"></script>
    <script src="js/common/common.js"></script>
</head>

<body>

    <!-- 顶部栏 -->
    <header class="header">
        <button class="back-button">
            <a href="choose_plug.html" class="back-icon"><i class="fa fa-angle-left"></i></a>
        </button>
        <div class="header-title"><i class="fa fa-bolt" aria-hidden="true"></i> </div>
    </header>

    <div class="content">


        <div class="status text-center">
            <img src="img/green-ball.gif" class="img-responsive center-block">
        </div>
        <div class="container price_card">

            <!-- 进度条 -->
            <div class="slider-container start">
                <div class="slider-info" id="sliderInfo" lang="en_US">sliderStartInfo</div>
                <input id="chargeSlider" type="text" class="w-100" data-slider-min="0" data-slider-max="100" data-slider-value="0" data-slider-step="1">
            </div>
        </div>
    </div>

    <!-- 页面加载动画 -->
    <div id="pageLoadingOverlay">
        <div class="charging-container">
            <i class="fa fa-car car-icon"></i>
            <div class="loading-text"></div>
        </div>
    </div>

    <!-- 加载动画 -->
    <div id="loadingOverlay">
        <div class="loading-spinner"></div>
        <div class="loading-text"></div>
    </div>

</body>
<script src="js/bootstrap.bundle.min.js"></script>
<script src="js/bootstrap-slider.min.js"></script>
<script>
    var currentSiteNumber = getLocalStorage('site'); // 场地编号
    var currentConnectorNumber = getLocalStorage('connector'); // 充电枪编号
    var settingToken = getLocalStorage('settingToken'); // 充电枪setting_token
    var connectorInfo = null; // 充电枪信息
    var lastUrl = 'choose_plug.html';
    var nextUrl = 'trail.html';

    $(document).ready(function () {
        // 初始化语言包，默认会回调pageReady函数(如存在)
        initLang();
    });

    // 页面初始化
    function pageReady() {
        // 如果依旧为空，则跳转至choose_plug.html
        if (isEmptyString(currentSiteNumber) || isEmptyString(currentConnectorNumber) || isEmptyString(settingToken)) {
            window.location.href = `choose_plug.html?site=${currentSiteNumber || getQueryVariable('site', '')}`;
            return;
        }
        if (!isLocalStorageAvailable()) {
            lastUrl += `?site=${currentSiteNumber}`;
            nextUrl += `?site=${currentSiteNumber}&connector=${currentConnectorNumber}`;
        }
        // 获取充电枪信息，并验证setting_token
        getConnector(currentConnectorNumber).then(() => {
            verifyConnectorStartCharge(currentConnectorNumber, settingToken);
        });

        // 初始化滑块
        const slider = new Slider('#chargeSlider', {
            formatter: function (value) {
                return value > 70 ? window.lang.translate('sliderStartInfo') : window.lang.translate('sliderStartInfo');
            },
            handle: 'round',
            tooltip: 'hide', // 隐藏默认提示
        });

        // 滑块提示文本元素
        const sliderInfo = document.getElementById('sliderInfo');

        // 加载指示器
        const loadingIndicator = document.getElementById('loadingIndicator');

        // 实现回弹效果
        let isSliding = false;

        slider.on('slideStart', function () {
            isSliding = true;
        });

        slider.on('slide', function (value) {
            const sliderEl = document.getElementById('chargeSlider');

            // 更新滑块上方的提示文本
            sliderInfo.textContent = value > 70 ? window.lang.translate('sliderStartInfo') : window.lang.translate('sliderStartInfo');
            sliderInfo.classList.toggle('active', value > 70);

            if (value > 70) {
                slider.setValue(75);
                sliderEl.closest('.slider-container').classList.add('slider-full');
            } else {
                sliderEl.closest('.slider-container').classList.remove('slider-full');
            }
        });

        slider.on('slideStop', function (value) {
            isSliding = false;

            const sliderEl = document.getElementById('chargeSlider');

            if (value > 70) {
                // 达到阈值，保持在100%
                slider.setValue(75);
                sliderEl.closest('.slider-container').classList.add('slider-full');
                sliderInfo.textContent = window.lang.translate('loading');
                sliderInfo.classList.add('active');

                // 模拟加载延迟，然后跳转到新页面
                setTimeout(() => {
                    // 替换为实际的跳转URL
                    window.location.href = nextUrl;
                }, 200);
            } else {
                // 未达到阈值，回弹到0
                slider.setValue(0);
                sliderEl.closest('.slider-container').classList.remove('slider-full');
                sliderInfo.textContent = window.lang.translate('sliderStartInfo');
                sliderInfo.classList.remove('active');
            }
        });
    }

    // 获取充电枪信息
    function getConnector(connectorNumber) {
        return new Promise((resolve, reject) => {
            ajaxPostRequestJsonAndCallback({
                url: "api/getConnector.php",
                params: {
                    language_code: currentLanguageCode,
                    connector_number: connectorNumber,
                },
                successCallback: function (data) {
                    connectorInfo = data.data;
                    $("title").text(connectorInfo.name);
                    $(".header > .header-title").append(connectorInfo.name);
                    $(".header > .back-button > a").attr("href", lastUrl);
                    if (connectorInfo?.connector_setting?.charge_tariff_scheme !== 'PRE_PAID') {
                        Swal.fire({
                            title: window.lang.translate('warning'),
                            text: window.lang.translate('notSupported'),
                            icon: 'warning',
                            showConfirmButton: false,
                            timer: 3000,
                            timerProgressBar: true,
                            willClose: function () {
                                // 跳回充电枪列表页
                                window.location.href = lastUrl;
                            }
                        });
                        reject(connectorInfo);
                    }
                    resolve(connectorInfo);
                },
                errorCallback: function (message) {
                    Swal.fire({
                        title: window.lang.translate('warning'),
                        confirmButtonText: window.lang.translate('btnConfirm'),
                        confirmButtonColor: '#54C49C',
                        text: message,
                        icon: 'error',
                        allowOutsideClick: false,
                        showCancelButton: true,
                        cancelButtonText: window.lang.translate('btnGoBack') // 自定义返回按钮文本
                    }).then(function (result) {
                        if (result.isConfirmed) {
                            // 点击确认按钮，刷新当前页面
                            window.location.reload();
                        } else if (result.dismiss === Swal.DismissReason.cancel) {
                            // 点击返回按钮，跳转到上一页
                            window.location.href = lastUrl;
                        }
                    });
                    reject(message);
                }
            });
        })
    }

    // 验证充电枪setting_token
    function verifyConnectorStartCharge(connectorNumber, settingToken) {
        return new Promise((resolve, reject) => {
            ajaxPostRequestJsonAndCallback({
                url: "api/verifyConnectorStartCharge.php",
                params: {
                    language_code: currentLanguageCode,
                    connector_number: connectorNumber,
                    setting_token: settingToken,
                },
                successCallback: function (data) {
                    if (data.data === true) {
                        resolve(data);
                    } else {
                        Swal.fire({
                            title: data?.message || window.lang.translate('warning'),
                            text: window.lang.translate('tryAgain'),
                            icon: 'warning',
                            showConfirmButton: false,
                            timer: 3000,
                            timerProgressBar: true,
                            willClose: function () {
                                // 跳回充电枪列表页
                                window.location.href = lastUrl;
                            }
                        });
                        reject(data);
                    }
                },
                errorCallback: function (message) {
                    Swal.fire({
                        title: message || window.lang.translate('warning'),
                        text: window.lang.translate('tryAgain'),
                        icon: 'warning',
                        showConfirmButton: false,
                        timer: 3000,
                        timerProgressBar: true,
                        willClose: function () {
                            // 跳回充电枪列表页
                            window.location.href = lastUrl;
                        }
                    });
                    reject(message);
                }
            });
        })
    }

</script>

</html>
