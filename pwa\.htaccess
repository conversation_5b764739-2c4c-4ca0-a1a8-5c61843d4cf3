<FilesMatch "(style.css|common.js)">
  Header set Cache-Control "no-cache, no-store, must-revalidate"
</FilesMatch>

<IfModule mod_rewrite.c>
  Options +FollowSymlinks -Multiviews
  RewriteEngine On

  # 允许访问
  Require all granted

  # 禁止目录列表
  Options -Indexes

  # 只处理根目录的访问
  RewriteCond %{REQUEST_URI} ^/pwa/?$ 
  RewriteRule ^$ site.html [L]

  # 线上环境配置（部署时取消注释，并注释掉上面的本地配置）
  # RewriteCond %{REQUEST_URI} ^/?$
  # RewriteRule ^$ site.html [L]
</IfModule>