<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <title></title>
    <link href="css/bootstrap.min.css" rel="stylesheet">
    <link href="css/font-awesome.min.css" rel="stylesheet">
    <link href="css/bootstrap-slider.min.css" rel="stylesheet">
    <script type="text/javascript">
        document.write('<link rel="stylesheet" href="css/style.css?time=' + new Date().getTime() + '">');
    </script>
    <script src="js/jquery-3.7.1.min.js"></script>
    <script src="js/jquery-lang.js" charset="utf-8" type="text/javascript"></script>
    <script src="js/sweetalert2.all.min.js"></script>
    <script src="js/bootstrap-input-spinner/bootstrap-input-spinner.js"></script>
    <script src="js/bootstrap-input-spinner/custom-editors.js"></script>
    <script src="js/big.min.js"></script>
    <script src="js/common/common.js"></script>
</head>

<body>

    <!-- 顶部栏 -->
    <header class="header">
        <button class="back-button">
            <a href="choose_plug.html" class="back-icon"><i class="fa fa-angle-left"></i></a>
        </button>
        <div class="header-title"></div>
        <div class="language-button">
            <div class="dropdown">
                <button class="btn btn-lang dropdown-toggle" type="button" id="langDropdown" data-bs-toggle="dropdown" aria-expanded="false">
                    <i class="fa fa-globe" aria-hidden="true"></i>
                </button>
                <ul class="dropdown-menu dropdown-menu-end" aria-labelledby="langDropdown">
                    <li><a class="dropdown-item" href="javascript:void(0);" data-lang="en_US" onclick="changeLanguage('en_US')">English</a></li>
                    <li><a class="dropdown-item" href="javascript:void(0);" data-lang="zh_HK" onclick="changeLanguage('zh_HK')">繁體中文</a></li>
                </ul>
            </div>
        </div>
    </header>

    <div class="content container py-4">
        <div class="row justify-content-center mb-5">
            <h1 class="text-center fw-bold ls-1" id="charge-title"></h1>
        </div>
        <div class="row justify-content-center mb-5">
            <div class="col-12 col-md-6">
                <input type="text" class="form-control-lg charge-input fs-1" required>
            </div>
        </div>
        <div class="row justify-content-center mb-3 mx-auto" id="estimated-amount-card">
            <div class="col-12 col-md-6 card card bg-light d-flex justify-content-center align-items-center h-100">
                <div class="card-body text-center">
                    <p class="text-secondary fw-bold mb-0 d-flex align-items-center justify-content-center" lang="en_US">estimatedAmount<span class="estimated-amount ms-4 fs-3" style="color:#11055E;"></span></p>
                </div>
            </div>
        </div>
        <div class="row justify-content-center mb-5" id="info-estimated-amount-card">
            <div class="col-12 col-md-6">
                <p class="text-danger fw-bold" id="info-estimated-amount" lang="en_US">infoEstimatedAmount</p>
            </div>
        </div>
        <div class="container price_card">
            <!-- 进度条 -->
            <div class="slider-container start">
                <div class="slider-info" id="sliderInfo" lang="en_US">sliderStartInfo</div>
                <input id="chargeSlider" type="text" class="w-100" data-slider-min="0" data-slider-max="100" data-slider-value="0" data-slider-step="1">
            </div>
        </div>
    </div>

    <!-- 页面加载动画 -->
    <div id="pageLoadingOverlay">
        <div class="charging-container">
            <i class="fa fa-car car-icon"></i>
            <div class="loading-text"></div>
        </div>
    </div>

    <!-- 加载动画 -->
    <div id="loadingOverlay">
        <div class="loading-spinner"></div>
        <div class="loading-text"></div>
    </div>

</body>
<script src="js/bootstrap.bundle.min.js"></script>
<script src="js/bootstrap-slider.min.js"></script>
<script>
    var currentSiteNumber = getLocalStorage('site'); // 场地编号
    var currentConnectorNumber = getLocalStorage('connector'); // 充电枪编号
    var settingToken = getLocalStorage('settingToken'); // 充电枪setting_token
    var isTime = false; // 是否为时间
    var isEnergy = false; // 是否为电量
    var selected = 0; // 已选值
    var step = 0; // 步长
    var max = 0; // 最大值
    const min = 0; // 最小值
    var connectorInfo = null; // 充电枪信息
    var lastUrl = 'choose_plug.html';
    var nextUrl = 'trail.html';

    var slider = null; // 滑块
    const debounceDelay = 1500; // 防抖延迟时间
    var debounceTimer = null; // 防抖定时器
    var currentRequest = null; // 当前请求

    // 滑块提示文本元素
    const sliderInfo = document.getElementById('sliderInfo');

    $(document).ready(function () {
        // 初始化语言包，默认会回调pageReady函数(如存在)
        initLang();
    });

    // 页面初始化
    function pageReady() {
        // 如果依旧为空，则跳转至choose_plug.html
        if (isEmptyString(currentSiteNumber) || isEmptyString(currentConnectorNumber)) {
            window.location.href = `choose_plug.html?site=${currentSiteNumber || getQueryVariable('site', '')}`;
            return;
        }
        if (!isLocalStorageAvailable()) {
            lastUrl += `?site=${currentSiteNumber}`;
            nextUrl += `?site=${currentSiteNumber}&connector=${currentConnectorNumber}`;
        }
        $("#charge-title").attr("lang", currentLanguageCode);
        // 获取充电枪信息，并验证setting_token
        getConnector(currentConnectorNumber).then(() => {
            verifyConnectorStartCharge(currentConnectorNumber, settingToken);

            isTime = connectorInfo?.connector_setting?.charge_value_type === 'TIME';
            isEnergy = connectorInfo?.connector_setting?.charge_value_type === 'ENERGY';
            const $chargeInput = $(".charge-input");
            var inputSpinnerConfig = {};
            // 时间
            if (isTime) {
                $("#charge-title").text(window.lang.translate('chargingDuration'));
                $("#confirm-charging").append(window.lang.translate('btnConfirmChargingDuration'));
                // 设置最大值，秒转分钟
                max = new Big(connectorInfo?.connector_setting?.pre_paid_charge_value_maximum_selection).div(60).round(0, Big.roundUp).toNumber();
                // 设置步长，秒转分钟
                step = new Big(connectorInfo?.connector_setting?.charge_value_interval).div(60).round(0, Big.roundUp).toNumber();
                inputSpinnerConfig.editor = customEditors.TimeEditor;
            } else if (isEnergy) {
                $("#charge-title").text(window.lang.translate('estimatedAmount'));
                $("#confirm-charging").text(window.lang.translate('btnConfirmChargingEnergy'));
                // 设置最大值，瓦转千瓦，保留最多三位小数
                max = new Big(connectorInfo?.connector_setting?.pre_paid_charge_value_maximum_selection).div(1000).round(3, Big.roundUp).toNumber();
                // 设置步长，瓦转千瓦，保留最多三位小数
                step = new Big(connectorInfo?.connector_setting?.charge_value_interval).div(1000).round(3, Big.roundUp).toNumber();
                // 电量添加单位显示
                $chargeInput.attr('data-suffix', window.lang.translate('unitKWh'));
                // 根据step的小数位，设置对应的data-decimals
                $chargeInput.attr('data-decimals', step.toString().split('.')[1]?.length || 0);
            }

            inputSpinnerConfig.buttonsClass = "btn btn-outline-success";
            inputSpinnerConfig.buttonsWidth = "5rem";
            inputSpinnerConfig.buttonsOnly = true;
            // 初始化 input-spinner
            $chargeInput.inputSpinner(inputSpinnerConfig);
            $chargeInput.attr('min', min).attr('max', max).attr('step', step);

            // 设置初始值
            $chargeInput.val(selected).trigger("change");
            getConnectorEstimatedAmount();

            // 监听输入变动
            $chargeInput.on("change", function () {
                let val = $(this).val();
                if (val > max) {
                    Swal.fire({
                        icon: "warning",
                        title: window.lang.translate('pleaseReselect'),
                        confirmButtonColor: "#54C49C"
                    });
                    val = max;
                }
                if (val < min) {
                    Swal.fire({
                        icon: "warning",
                        title: window.lang.translate('pleaseReselect'),
                        confirmButtonColor: "#54C49C"
                    });
                    val = min;
                }
                selected = val;
                $(this).val(selected);

                // 请求预估金额
                $(".estimated-amount").addClass('loading-dots').html(`
                    <span>•</span>
                    <span>•</span>
                    <span>•</span>
                `);
                clearTimeout(debounceTimer); // 取消上次请求
                debounceTimer = setTimeout(getConnectorEstimatedAmount, debounceDelay);
            });
        });

        // 初始化滑块
        slider = new Slider('#chargeSlider', {
            formatter: function (value) {
                return value > 70 ? window.lang.translate('sliderStartInfo') : window.lang.translate('sliderStartInfo');
            },
            handle: 'round',
            tooltip: 'hide', // 隐藏默认提示
        });

        // 加载指示器
        const loadingIndicator = document.getElementById('loadingIndicator');

        // 实现回弹效果
        let isSliding = false;

        slider.on('slideStart', function () {
            isSliding = true;
        });

        slider.on('slide', function (value) {
            const sliderEl = document.getElementById('chargeSlider');

            // 更新滑块上方的提示文本
            sliderInfo.textContent = value > 70 ? window.lang.translate('sliderStartInfo') : window.lang.translate('sliderStartInfo');
            sliderInfo.classList.toggle('active', value > 70);

            if (value > 70) {
                slider.setValue(75);
                sliderEl.closest('.slider-container').classList.add('slider-full');
            } else {
                sliderEl.closest('.slider-container').classList.remove('slider-full');
            }
        });

        slider.on('slideStop', function (value) {
            isSliding = false;

            const sliderEl = document.getElementById('chargeSlider');

            if (value > 70) {
                // 达到阈值，保持在100%
                slider.setValue(75);
                sliderEl.closest('.slider-container').classList.add('slider-full');
                sliderInfo.textContent = window.lang.translate('loading');
                sliderInfo.classList.add('active');

                // 模拟加载延迟，然后跳转到新页面
                setTimeout(() => {
                    confirmCharging();
                }, 200);
            } else {
                // 未达到阈值，回弹到0
                slider.setValue(0);
                sliderEl.closest('.slider-container').classList.remove('slider-full');
                sliderInfo.textContent = window.lang.translate('sliderStartInfo');
                sliderInfo.classList.remove('active');
            }
        });

    }

    // 获取充电枪信息
    function getConnector(connectorNumber) {
        return new Promise((resolve, reject) => {
            ajaxPostRequestJsonAndCallback({
                url: "api/getConnector.php",
                params: {
                    language_code: currentLanguageCode,
                    connector_number: connectorNumber,
                },
                successCallback: function (data) {
                    connectorInfo = data.data;
                    $("title").text(connectorInfo.name);
                    $(".header > .header-title").append(connectorInfo.name);
                    $(".header > .back-button > a").attr("href", lastUrl);
                    if (connectorInfo?.connector_setting?.charge_tariff_scheme !== 'PRE_PAID') {
                        Swal.fire({
                            title: window.lang.translate('warning'),
                            text: window.lang.translate('notSupported'),
                            icon: 'warning',
                            showConfirmButton: false,
                            timer: 3000,
                            timerProgressBar: true,
                            willClose: function () {
                                // 跳回充电枪列表页
                                window.location.href = lastUrl;
                            }
                        });
                        reject(connectorInfo);
                    }
                    resolve(connectorInfo);
                },
                errorCallback: function (message) {
                    Swal.fire({
                        title: window.lang.translate('warning'),
                        confirmButtonText: window.lang.translate('btnConfirm'),
                        confirmButtonColor: '#54C49C',
                        text: message,
                        icon: 'error',
                        allowOutsideClick: false,
                        showCancelButton: true,
                        cancelButtonText: window.lang.translate('btnGoBack') // 自定义返回按钮文本
                    }).then(function (result) {
                        if (result.isConfirmed) {
                            // 点击确认按钮，刷新当前页面
                            window.location.reload();
                        } else if (result.dismiss === Swal.DismissReason.cancel) {
                            // 点击返回按钮，跳转到上一页
                            window.location.href = lastUrl;
                        }
                    });
                    reject(message);
                }
            });
        })
    }

    // 验证充电枪setting_token
    function verifyConnectorStartCharge(connectorNumber, settingToken) {
        return new Promise((resolve, reject) => {
            ajaxPostRequestJsonAndCallback({
                url: "api/verifyConnectorStartCharge.php",
                params: {
                    language_code: currentLanguageCode,
                    connector_number: connectorNumber,
                    setting_token: settingToken,
                },
                successCallback: function (data) {
                    if (data.data === true) {
                        resolve(data);
                    } else {
                        Swal.fire({
                            title: data?.message || window.lang.translate('warning'),
                            text: window.lang.translate('tryAgain'),
                            icon: 'warning',
                            showConfirmButton: false,
                            timer: 3000,
                            timerProgressBar: true,
                            willClose: function () {
                                // 跳回充电枪列表页
                                window.location.href = lastUrl;
                            }
                        });
                        reject(data);
                    }
                },
                errorCallback: function (message) {
                    Swal.fire({
                        title: message || window.lang.translate('warning'),
                        text: window.lang.translate('tryAgain'),
                        icon: 'warning',
                        showConfirmButton: false,
                        timer: 3000,
                        timerProgressBar: true,
                        willClose: function () {
                            // 跳回充电枪列表页
                            window.location.href = lastUrl;
                        }
                    });
                    reject(message);
                }
            });
        })
    }

    function getConnectorEstimatedAmount() {
        // 如果还有没结束的请求，直接结束
        if (currentRequest) {
            currentRequest.abort();
            currentRequest = null;
        }

        if (isEmptyString(selected)) {
            return;
        }

        if (selected === '0' || selected === 0) {
            $(".estimated-amount").removeClass('loading-dots').text(
                selected.toLocaleString('zh-HK', {
                    style: 'currency',
                    currency: 'HKD',
                    minimumFractionDigits: 1,
                    maximumFractionDigits: 1,
                    minimumIntegerDigits: 1
                }).replace('HK$', 'HK$ ')
            );
            return;
        }

        if (isTime) {
            var prePaidSelectedChargeValue = selected * 60;
        } else if (isEnergy) {
            var prePaidSelectedChargeValue = selected * 1000;
        }

        currentRequest = ajaxPostRequestJsonAndCallback({
            url: "api/getConnectorEstimatedAmount.php",
            async: true,
            params: {
                language_code: currentLanguageCode,
                connector_number: currentConnectorNumber,
                pre_paid_selected_charge_value: prePaidSelectedChargeValue,
            },
            successCallback: function (data) {
                if (!isEmptyString(data.data?.charge_value_amount)) {
                    // 金额分转元，保留一位小数，转为数字
                    var amount = new Big(data.data.charge_value_amount).div(100).toNumber();
                    // 金额格式化
                    amount = amount.toLocaleString('zh-HK', {
                        style: 'currency',
                        currency: 'HKD',
                        minimumFractionDigits: 1,
                        maximumFractionDigits: 1,
                        minimumIntegerDigits: 1
                    }).replace('HK$', 'HK$ ');
                    $(".estimated-amount").removeClass('loading-dots').text(amount);
                }
            },
            errorCallback: function (message) {
                // 手动取消不弹窗
                if (message === 'abort') {
                    return;
                }
                Swal.fire({
                    title: window.lang.translate('warning'),
                    text: window.lang.translate('requestEstimatedAmountFailed'),
                    icon: 'warning',
                    allowOutsideClick: false,
                    showCancelButton: true,
                    confirmButtonText: window.lang.translate('btnRetry'),
                    confirmButtonColor: '#54C49C',
                    cancelButtonText: window.lang.translate('cancel'),
                }).then(function (result) {
                    if (result.isConfirmed) {
                        getConnectorEstimatedAmount();
                    }
                });
            },
            completeCallback: function () {
                currentRequest = null;
            }
        });
    }

    // 确认充电
    function confirmCharging() {
        validate().then(() => {
            var prePaidSelectedChargeValue = selected;
            if (isTime) {
                prePaidSelectedChargeValue *= 60;
            } else if (isEnergy) {
                prePaidSelectedChargeValue *= 1000;
            }
            setLocalStorage('prePaidSelectedChargeValue', prePaidSelectedChargeValue);
            window.location.href = nextUrl;
        }).catch((message = null) => {
            if (message) {
                Swal.fire({
                    title: window.lang.translate('warning'),
                    text: message,
                    icon: 'warning',
                    showConfirmButton: false,
                    timer: 2000,
                    timerProgressBar: true,
                });
            }
        }).finally(() => {
            // 重置
            const sliderEl = document.getElementById('chargeSlider');
            slider.setValue(0);
            sliderEl.closest('.slider-container').classList.remove('slider-full');
            sliderInfo.textContent = window.lang.translate('sliderStartInfo');
            sliderInfo.classList.remove('active');
        });
    }

    function validate() {
        // 验证
        return new Promise((resolve, reject) => {
            // 非时间非电量禁止开始充电
            if (!isTime && !isEnergy) {
                Swal.fire({
                    title: window.lang.translate('warning'),
                    text: window.lang.translate('notSupported'),
                    icon: 'warning',
                    showConfirmButton: false,
                    timer: 2000,
                    timerProgressBar: true,
                    willClose: function () {
                        // 跳回充电枪列表页
                        window.location.href = lastUrl;
                    }
                });
                reject();
            }
            // 超出范围禁止开始充电
            if (selected < min || selected > max) {
                reject(window.lang.translate('pleaseReselect'));
            }
            // 时间
            if (isTime) {
                // 未选择
                if (isEmptyString(selected) || selected === '0') {
                    reject(window.lang.translate('pleaseSelectATime'));
                }
                // 如果不是时间格式
                if (/^\d+:(?:[0-5][0-9])$/.test(selected)) {
                    reject(window.lang.translate('pleaseReselect'));
                }
            }
            // 电量
            if (isEnergy) {
                // 未选择
                if (isEmptyString(selected) || selected === '0') {
                    reject(window.lang.translate('pleaseSelectAnEnergy'));
                }
                // 如果不是数字
                if (!/^\d+(\.\d+)?$/.test(selected)) {
                    reject(window.lang.translate('pleaseReselect'));
                }
            }
            resolve();
        });
    }

</script>

</html>
