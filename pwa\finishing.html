<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <title></title>
    <link href="css/bootstrap.min.css" rel="stylesheet">
    <link href="css/font-awesome.min.css" rel="stylesheet">
    <link href="css/bootstrap-slider.min.css" rel="stylesheet">
    <script type="text/javascript">
        document.write('<link rel="stylesheet" href="css/style.css?time=' + new Date().getTime() + '">');
    </script>
    <script src="js/jquery-3.7.1.min.js"></script>
    <script src="js/jquery-lang.js" charset="utf-8" type="text/javascript"></script>
    <script src="js/sweetalert2.all.min.js"></script>
    <script src="js/common/common.js"></script>
</head>

<body>

    <!-- 顶部栏 -->
    <header class="header">
        <button class="back-button">
            <a href="javascript:void(0)" class="back-icon">×</a>
        </button>
        <div class="header-title"><i class="fa fa-bolt" aria-hidden="true"></i> </div>
    </header>

    <div class="content charging_content">
        <div class="status text-center">
            <img src="img/charging_complete.gif" class="img-responsive center-block">
        </div>

        <div class="status completed text-center"><b lang="en_US">completed</b></div>
        <div class="container ">
            <div class="row info_card mb-5">
                <div class="col-6">
                    <div class="block time">
                        <div class="time_content">
                            00m
                        </div>
                        <div class="time_title text-center" lang="en_US">chargedTime</div>
                    </div>
                </div>
                <div class="col-6">
                    <div class="block energy">
                        <div class="energy_content">
                            0kWh
                        </div>
                        <div class="time_title text-center" lang="en_US">chargedEnergy</div>
                    </div>
                </div>
            </div>

        </div>

        <div class="fixed_content">
            <!-- 卡片容器 -->
            <div class="card-container est_card price_card">
                <!-- 卡片 -->
                <div class="card">
                    <div class="card-body">
                        <div class="row">
                            <div class="col-8">
                                <span class="title" lang="en_US">estimatedTotal</span><br>
                                <span class="price"><b>HK$</b> 0.2</span>
                            </div>
                            <div class="col-4">
                                <a href="#" class="float-end">
                                    <i class="fa fa-refresh" aria-hidden="true"></i>
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <script>
            var currentSiteNumber = getLocalStorage('site'); // 场地编号
            var currentConnectorNumber = getLocalStorage('connector'); // 充电枪编号
            var connectorInfo = null; // 充电枪信息
            var lastUrl = 'site.html';
            var nextUrl = 'site.html';

            $(document).ready(function () {
                // 初始化语言包，默认会回调pageReady函数(如存在)
                initLang();
            });

            // 页面初始化
            function pageReady() {
                // 如果为空，则跳转至site.html
                if (isEmptyString(currentSiteNumber) || isEmptyString(currentConnectorNumber)) {
                    window.location.href = `site.html`;
                    return;
                }

                // 获取充电枪信息
                getConnector(currentConnectorNumber);
            }

            // 获取充电枪信息
            function getConnector(connectorNumber) {
                return new Promise((resolve, reject) => {
                    ajaxPostRequestJsonAndCallback({
                        url: "api/getConnector.php",
                        params: {
                            language_code: currentLanguageCode,
                            connector_number: connectorNumber,
                        },
                        successCallback: function (data) {
                            connectorInfo = data.data;
                            $("title").text(connectorInfo.name);
                            $(".header > .header-title").append(connectorInfo.name);
                            $(".header > .back-button > a").attr("href", lastUrl);
                            resolve(connectorInfo);
                        },
                        errorCallback: function (message) {
                            Swal.fire({
                                title: window.lang.translate('warning'),
                                confirmButtonText: window.lang.translate('btnConfirm'),
                                confirmButtonColor: '#54C49C',
                                text: message,
                                icon: 'error',
                                allowOutsideClick: false,
                                showCancelButton: true,
                                cancelButtonText: window.lang.translate('btnGoBack') // 自定义返回按钮文本
                            }).then(function (result) {
                                if (result.isConfirmed) {
                                    // 点击确认按钮，刷新当前页面
                                    window.location.reload();
                                } else if (result.dismiss === Swal.DismissReason.cancel) {
                                    // 点击返回按钮，跳转到上一页
                                    window.location.href = lastUrl;
                                }
                            });
                            reject(message);
                        }
                    });
                })
            }
        </script>
</body>

</html>
