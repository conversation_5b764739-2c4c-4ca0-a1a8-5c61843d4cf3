<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <title lang="en_US">webTitleSite</title>
    <link href="css/bootstrap.min.css" rel="stylesheet">
    <link href="css/font-awesome.min.css" rel="stylesheet">
    <script type="text/javascript">
        document.write('<link rel="stylesheet" href="css/style.css?time=' + new Date().getTime() + '">');
    </script>
    <script src="js/jquery-3.7.1.min.js"></script>
    <script src="js/jquery-lang.js" charset="utf-8" type="text/javascript"></script>
    <script src="js/sweetalert2.all.min.js"></script>
    <link rel="stylesheet" href="css/mescroll.min.css">
    <script src="js/mescroll.min.js" charset="utf-8"></script>
    <script src="js/common/common.js"></script>
</head>

<body>

    <!-- 顶部栏 -->
    <header class="header">
        <div class="header-title" lang="en_US">site</div>
        <div class="language-button">
            <div class="dropdown">
                <button class="btn btn-lang dropdown-toggle" type="button" id="langDropdown" data-bs-toggle="dropdown" aria-expanded="false">
                    <i class="fa fa-globe" aria-hidden="true"></i>
                </button>
                <ul class="dropdown-menu dropdown-menu-end" aria-labelledby="langDropdown">
                    <li><a class="dropdown-item" href="javascript:void(0);" data-lang="en_US" onclick="changeLanguage('en_US')">English</a></li>
                    <li><a class="dropdown-item" href="javascript:void(0);" data-lang="zh_HK" onclick="changeLanguage('zh_HK')">繁體中文</a></li>
                </ul>
            </div>
        </div>
    </header>

    <div class="content">

        <div class="container site">
            <div class="search-container">
                <input id="site-search" type="search" class="search-input" onsearch="searchSite()" placeholder="placeHolderSite" lang="en_US" data-lang-content="false" />
                <button type="button" class="search-button" onclick="searchSite()">
                    <i class="fa fa-search search-icon"></i>
                </button>
            </div>

            <div class="filter-container">
                <button type="button" class="filter-button" onclick="pageSiteOrderByDistance()">
                    <img src="img/filter.svg">
                </button>
            </div>
        </div>

        <div class="mescroll" id="mescroll">
            <!-- Site Card -->
            <div class="mescroll-box"></div>
        </div>

        <!-- <div class="bottom-nav">
            <div class="nav-item active">
                <a href="site.html">
                    <span class="icon">
                        <img src="img/charge.svg">
                    </span>
                    <span class="label" lang="en_US">charge</span>
                </a>
            </div>
            <div class="nav-item">
                <a href="record_auth.html">
                    <span class="icon">
                        <img src="img/record.svg">
                    </span>
                    <span class="label" lang="en_US">record</span>
                </a>
            </div>
        </div> -->
    </div>

    <!-- 遮罩层 -->
    <div class="mask hide" id="mask"></div>

    <!-- 页面加载动画 -->
    <div id="pageLoadingOverlay">
        <div class="charging-container">
            <i class="fa fa-car car-icon"></i>
            <div class="loading-text"></div>
        </div>
    </div>

    <!-- 加载动画 -->
    <div id="loadingOverlay">
        <div class="loading-spinner"></div>
        <div class="loading-text"></div>
    </div>

    <!-- 底部菜单 -->
    <div class="bottom-menu" id="map-menu">
        <button class="bottom-menu-item hide" id="google-map-btn" onclick="openMap(this)" lang="en_US">googleMap</button>
        <button class="bottom-menu-item hide" id="apple-map-btn" onclick="openMap(this)" lang="en_US">appleMap</button>
        <button class="bottom-menu-item hide" id="amap-btn" onclick="openMap(this)" lang="en_US">amap</button>
        <button class="bottom-menu-item" id="map-menu-cancel-btn" onclick="closeMapMenu()" lang="en_US">btnCancel</button>
    </div>

</body>

</html>

<script src="js/bootstrap.bundle.min.js"></script>
<script>
    const sitePlaceholderImage = 'img/placeholder_site.png';
    var currentLongitude = getLocalStorage('currentLongitude'); // 经度
    var currentLatitude = getLocalStorage('currentLatitude'); // 纬度
    var isSortDistance = getLocalStorage('isSortDistance', '0') === '1' ? true : false;// 是否按照距离排序
    var current = 1; // 当前页码
    var size = 10; // 分页数据条数
    var total = 0; // 总共条数
    var pages = 0; // 总分页数量
    var mescroll = null; // 下拉上拉插件

    $(document).ready(function () {
        // 初始化语言包，默认会回调pageReady函数(如存在)
        initLang();
    });

    // 页面初始化
    function pageReady() {
        removeLocalStorage('site');
        removeLocalStorage('connector');
        removeLocalStorage('settingToken');
        if (isSortDistance) {
            $('.filter-button').addClass('active');
        }
        // 下拉上拉插件
        mescroll = initMeScroll(downCallback, pageSite, upCallback, current, size);
    }

    // 获取站点列表
    function pageSite(page = null) {
        var params = {
            language_code: currentLanguageCode,
            keyword: $("#site-search").val(),
            current: page?.num || current,
            size: page?.size || size,
        };
        if (isSortDistance) {
            params.longitude = currentLongitude;
            params.latitude = currentLatitude;
        }

        ajaxPostRequestJsonAndCallback({
            url: "api/pageSite.php",
            params: params,
            successCallback: function (data) {
                var result = data.data;
                current = result.current;
                size = result.size;
                total = result.total;
                pages = result.pages;
                if (mescroll) {
                    mescroll.endByPage(result?.records.length, pages);
                }
                // 第一页清空
                if ((page?.num || current) == 1) {
                    $('#mescroll .mescroll-box').html('');
                }
                if (isArrayAndNotEmpty(result.records)) {
                    const siteList = result.records;
                    var siteHtml = ``;
                    for (let index = 0; index < siteList.length; index++) {
                        const site = siteList[index];
                        var siteMainImage = sitePlaceholderImage;
                        if (!isEmptyString(site.main_image_url)) {
                            siteMainImage = site.main_image_url;
                        }
                        var isShowDistance = !isEmptyString(site.google_map_url) || !isEmptyString(site.apple_map_url) || !isEmptyString(site.amap_map_url);

                        siteHtml += `
                            <div class="position-relative site_card d-flex align-items-start" onclick="goChoosePlugPage('${site.site_number}')">
                                <img imgurl="${siteMainImage}" src="${sitePlaceholderImage}" class="site_img">
                                <div class="info-content flex-grow-1">
                                    <h2>${site.name}</h2>
                                    <p class="text-secondary mb-1"><span><i class="fa fa-bolt me-2" aria-hidden="true"></i>${site.vacancy_connector_count} ${window.lang.translate('available')}</span>  / ${site.connector_count}</p>
                                </div>
                                <a href="javascript:void(0);" onclick="event.stopPropagation();goSiteDetailPage('${site.site_number}')" class="icon-search"><img src="img/view_detail.svg"></a>
                                <div class="distance-btn ${isShowDistance ? '' : 'hide'}" onclick="event.stopPropagation();openMapMenu('${site.google_map_url}', '${site.apple_map_url}', '${site.amap_map_url}')">
                                    <i class="fa fa-location-arrow me-2" aria-hidden="true"></i>${getSiteDistance(site.longitude, site.latitude)}
                                </div>
                            </div>
                        `;
                    }
                    if (!page && mescroll) {
                        mescroll.endSuccess();
                    }
                    $('#mescroll .mescroll-box').append(siteHtml);
                }
            },
            beforeErrorCallback: function () {
                if (mescroll) {
                    mescroll.endErr();
                }
            },
            errorCallback: function (message) {
                Swal.fire({
                    title: message || window.lang.translate('warning'),
                    text: window.lang.translate('requestFailed'),
                    icon: 'warning',
                    allowOutsideClick: false,
                    showCancelButton: true,
                    confirmButtonText: window.lang.translate('btnRetry'),
                    confirmButtonColor: '#54C49C',
                    cancelButtonText: window.lang.translate('cancel') // 自定义返回按钮文本
                }).then(function (result) {
                    if (result.isConfirmed) {
                        mescroll.resetUpScroll();
                    } else {
                        // 刷新页面
                        window.location.reload();
                    }
                });
            }
        });
    }

    // 按照距离排序
    function pageSiteOrderByDistance() {
        isSortDistance = !isSortDistance;
        setLocalStorage('isSortDistance', isSortDistance ? 1 : 0);

        if (isSortDistance) {
            getLocation().then(({ longitude, latitude }) => {
                currentLongitude = longitude;
                currentLatitude = latitude;
                setLocalStorage('currentLongitude', currentLongitude);
                setLocalStorage('currentLatitude', currentLatitude);
                $('.filter-button').addClass('active');
                mescroll.resetUpScroll();
            }).catch((error) => {
                // 获取位置失败，取消距离排序
                $('.filter-button').removeClass('active');
                isSortDistance = false;
                setLocalStorage('isSortDistance', isSortDistance ? 1 : 0);
                console.error("Error occurred while obtaining location:", error);
            });
        } else {
            $('.filter-button').removeClass('active');
            pageSite();
        }

    }

    // 获取site和当前位置的距离
    function getSiteDistance(longitude, latitude) {
        // 未开启距离排序，无法获取当前位置
        if (!isSortDistance || !longitude || !latitude) {
            return window.lang.translate('navigation');
        }

        // 地球半径（米）
        const R = 6371000;
        // 将经纬度转换为弧度
        const lat1Rad = latitude * Math.PI / 180;
        const lon1Rad = longitude * Math.PI / 180;
        const lat2Rad = currentLatitude * Math.PI / 180;
        const lon2Rad = currentLongitude * Math.PI / 180;

        // 计算经纬度的差值
        const deltaLat = lat2Rad - lat1Rad;
        const deltaLon = lon2Rad - lon1Rad;

        // 使用球面三角公式计算距离
        const a = Math.sin(deltaLat / 2) * Math.sin(deltaLat / 2) +
            Math.cos(lat1Rad) * Math.cos(lat2Rad) *
            Math.sin(deltaLon / 2) * Math.sin(deltaLon / 2);
        const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
        const distance = R * c;

        if (distance >= 1000) {
            // 转换为公里并保留一位小数
            return (distance / 1000).toFixed(1) + 'km';
        } else {
            // 小于 1000 米直接显示米数
            return Math.round(distance) + 'm';
        }
    }

    // 搜索
    function searchSite() {
        mescroll.resetUpScroll();
    }

    // 下拉刷新
    function downCallback() {
        mescroll.resetUpScroll();
    }

    // 上拉加载
    function upCallback(page) {
        pageSite(page);
    }

    // 跳转充电枪列表页
    function goChoosePlugPage(siteNumber) {
        if (isLocalStorageAvailable()) {
            setLocalStorage('site', siteNumber);
            window.location.href = `choose_plug.html`;
            return;
        }
        window.location.href = `choose_plug.html?site=${siteNumber}`;
    }

    // 跳转站点详情页
    function goSiteDetailPage(siteNumber) {
        if (isLocalStorageAvailable()) {
            setLocalStorage('site', siteNumber);
            window.location.href = `site_detail.html`;
            return;
        }
        window.location.href = `site_detail.html?site=${siteNumber}`;
    }

    // 打开地图菜单
    function openMapMenu(googleMapUrl = null, appleMapUrl = null, amapMapUrl = null) {
        if (isEmptyString(googleMapUrl) && isEmptyString(appleMapUrl) && isEmptyString(amapMapUrl)) {
            return;
        }
        if (!isEmptyString(googleMapUrl)) {
            $('#google-map-btn').removeClass('hide').data('map-url', googleMapUrl);
        }
        if (!isEmptyString(appleMapUrl)) {
            $('#apple-map-btn').removeClass('hide').data('map-url', appleMapUrl);
        }
        if (!isEmptyString(amapMapUrl)) {
            $('#amap-btn').removeClass('hide').data('map-url', amapMapUrl);
        }
        $('#mask').removeClass('hide');
        // 遮罩层添加一次性关闭事件
        $('#mask').one('click', function () {
            closeMapMenu();
        });

        openBottomMenu('#map-menu');
    }

    // 关闭地图菜单
    function closeMapMenu() {
        $('#google-map-btn, #apple-map-btn, #amap-btn').addClass('hide').data('map-url', null);
        $('#mask').addClass('hide');
        closeBottomMenu('#map-menu');
    }

    // 打开地图
    function openMap(that) {
        const mapUrl = $(that).data('map-url');
        if (isEmptyString(mapUrl)) {
            return;
        }
        window.open(mapUrl, '_blank');
        closeMapMenu();
    }

</script>
