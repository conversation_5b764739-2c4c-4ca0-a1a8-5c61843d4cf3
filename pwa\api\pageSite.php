<?php
include_once(__DIR__ . '/common.php');

$post = file_get_contents('php://input');
$post = json_decode($post, true);

$post_data = [
    'language_code' => input($post['language_code']) ?? DEFAULT_LANGUAGE_CODE,
    'keyword' => input($post['keyword']),
    'current' => is_int($post['current']) ? $post['current'] : 1,
    'size' => is_int($post['size']) ? $post['size'] : 10,
];

if (isset($post['latitude']) && isset($post['longitude']) && is_numeric($post['latitude']) && is_numeric($post['longitude'])) {
    $post_data['latitude'] = $post['latitude'];
    $post_data['longitude'] = $post['longitude'];
}

$post_url = API_URL . 'pageSite';
$response = curlPost($post_url, $post_data);

$result = json_decode($response, true);

writeLog('Request URL: ' . $post_url . ' Params: ' . json($post_data) . ', Response: ' . $response);
if (!is_array($result)) {
    returnJson([
        'code' => 500,
        'message' => 'Data Exception',
    ]);
}

returnJson($result);