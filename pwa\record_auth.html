<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="utf-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
  <title>Charge Record</title>
  <link href="css/bootstrap.min.css" rel="stylesheet">
  <link href="https://cdn.jsdelivr.net/npm/font-awesome@4.7.0/css/font-awesome.min.css" rel="stylesheet">
    <script type="text/javascript">
            document.write('<link rel="stylesheet" href="css/style.css?time=' + new Date().getTime() + '">');
 </script>
</head>
<body>

  <!-- 顶部栏 -->
    <header class="header">
      <button class="back-button">
            <a href="site.html" class="back-icon"><i class="fa fa-angle-left"></i></a>
        </button>
        <div class="header-title">Charge Record</div>
    </header>

  <div class="content auth_content">

  <div class="container">
    <div class="title">Please enter the last four digits of the card number for verification</div>
        <div class="card-input">
            <input type="tel" class="input-box" id="code1" maxlength="1" autofocus>
            <input type="tel" class="input-box" id="code2" maxlength="1">
            <input type="tel" class="input-box" id="code3" maxlength="1">
            <input type="tel" class="input-box" id="code4" maxlength="1">
        </div>
        <button class="submit-btn" onclick="validateCode()">Confirm</button>
        <div class="error-message" id="errorMsg">Card number does not exist or entered incorrectly, please re-enter</div>
  </div>

  <div class="bottom-nav">
        <div class="nav-item ">
          <a href="site.html">
            <span class="icon">
              <img src="img/charge.svg">
            </span>
            <span class="label">Charge</span>
          </a>
        </div>
        <div class="nav-item active">
          <a href="record.html">
            <span class="icon">
              <img src="img/record.svg">
            </span>
            <span class="label">Record</span>
          </a>
        </div>
    </div>
</div>
</body>
<script>
      // 正确的卡号尾号（可修改为实际号码）
        const CORRECT_CODE = "1234";

        // 自动切换输入框焦点
        function autoFocusNext(input, nextId) {
            input.addEventListener('input', (e) => {
                if (e.target.value.length === 1 && nextId) {
                    document.getElementById(nextId).focus();
                }
            });
        }

        // 初始化焦点和事件
        window.onload = () => {
            const inputs = document.querySelectorAll('.input-box');
            autoFocusNext(inputs[0], 'code2');
            autoFocusNext(inputs[1], 'code3');
            autoFocusNext(inputs[2], 'code4');
        };

        // 验证函数
        function validateCode() {
            const code1 = document.getElementById('code1').value;
            const code2 = document.getElementById('code2').value;
            const code3 = document.getElementById('code3').value;
            const code4 = document.getElementById('code4').value;
            const enteredCode = code1 + code2 + code3 + code4;

            const errorMsg = document.getElementById('errorMsg');
            const inputs = document.querySelectorAll('.input-box');

            if (enteredCode.length !== 4) {
                errorMsg.style.display = 'block';
                inputs.forEach(input => input.classList.add('error'));
                return;
            }

            if (enteredCode === CORRECT_CODE) {
                // 验证通过，跳转到下一页（可替换为实际URL）
                window.location.href = 'record.html';
            } else {
                errorMsg.style.display = 'block';
                inputs.forEach(input => input.classList.add('error'));
                // 清空输入框并移除错误样式（可选）
                setTimeout(() => {
                    inputs.forEach(input => {
                        input.value = '';
                        input.classList.remove('error');
                    });
                    errorMsg.style.display = 'none';
                    document.getElementById('code1').focus();
                }, 3000);
            }
        }
</script>
</html>