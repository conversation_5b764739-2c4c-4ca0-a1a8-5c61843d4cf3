<?php
include_once(__DIR__ . '/common.php');

$post = file_get_contents('php://input');
$post = json_decode($post, true);

$post_data = [
    'language_code' => input($post['language_code']) ?? DEFAULT_LANGUAGE_CODE,
    'connector_number' => input($post['connector_number']),
    'pre_paid_selected_charge_value' => input($post['pre_paid_selected_charge_value']),
];

$post_url = API_URL . 'getConnectorEstimatedAmount';
$response = curlPost($post_url, $post_data);

$result = json_decode($response, true);

writeLog('Request URL: ' . $post_url . ' Params: ' . json($post_data) . ', Response: ' . $response);
if (!is_array($result)) {
    returnJson([
        'code' => 500,
        'message' => 'Data Exception',
    ]);
}

returnJson($result);