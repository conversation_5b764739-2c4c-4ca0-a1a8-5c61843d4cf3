<?php
// 注意！！！
// 此方法不是直接调用的，而是被其他方法调用的
include_once(__DIR__ . '/common.php');

$post = file_get_contents('php://input');
$post = json_decode($post, true);

// 载入枚举
if (!isset($charge_point_action)) {
    returnJson([
        'code' => 500,
        'message' => 'Data Exception',
    ]);
}

$post_data = [
    'language_code' => input($post['language_code']) ?? DEFAULT_LANGUAGE_CODE,
    'unique_id' => input($post['unique_id']),
    'charge_point_action' => $charge_point_action,
];

$post_url = API_URL . 'getCallChargePointRecord';
$response = curlPost($post_url, $post_data);

$result = json_decode($response, true);

writeLog('Request URL: ' . $post_url . ', Params: ' . json($post_data) . ', Response: ' . $response);
if (!is_array($result)) {
    returnJson([
        'code' => 500,
        'message' => 'Data Exception',
    ]);
}

returnJson($result);